<template>
  <div class="transfer-page" :class="{ 'dark-theme': appStore.isDarkMode, 'light-theme': !appStore.isDarkMode, 'sidebar-open': appStore.isSidebarOpen }">
    <!-- 移动端遮罩层 -->
    <div 
      v-if="appStore.isSidebarOpen && isMobile" 
      class="sidebar-overlay"
      @click="appStore.closeSidebar"
    ></div>

    <!-- 左侧设备列表（PC端常显，移动端侧边栏） -->
    <aside class="device-sidebar" :class="{ 'mobile-sidebar': isMobile }">
      <!-- 左侧栏Logo -->
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <img src="/logo.svg" alt="WSLink Logo" />
          <span>WSLink</span>
        </div>
      </div>
      
      <!-- 分割线 -->
      <div class="sidebar-divider"></div>
      
      <!-- 已连接设备标题 -->
      <div class="connected-devices-title">
        连接的设备
      </div>
      
      <!-- 分割线 -->
      <div class="sidebar-divider"></div>
      
      <!-- 已连接设备列表 -->
      <div class="connected-devices">
        <div 
          v-for="device in deviceStore.connectedDevices"
          :key="device.id"
          class="device-item"
          :class="{ 'active': messageStore.activeDeviceId === device.id }"
          @click="selectDevice(device)"
        >
          <div class="device-avatar">
            <el-icon size="20">
              <Monitor v-if="device.type === 'desktop'" />
              <Iphone v-else />
            </el-icon>
          </div>
          <div class="device-info">
            <div class="device-name">{{ device.name }}</div>
            <div class="device-status">局域网</div>
          </div>
          <button 
            class="disconnect-action"
            @click.stop="disconnectDevice(device.id)"
          >
            断开
          </button>
        </div>
        
        <!-- 默认显示示例设备 -->
        <div v-if="deviceStore.connectedDevices.length === 0">
          <div class="device-item active">
            <div class="device-avatar">
              <el-icon size="20"><Iphone /></el-icon>
            </div>
            <div class="device-info">
              <div class="device-name">mei-chao</div>
              <div class="device-status">局域网</div>
            </div>
            <button class="disconnect-action">断开</button>
          </div>
        </div>
        
        <div v-if="deviceStore.connectedDevices.length === 0" class="empty-devices">
          <el-icon><Connection /></el-icon>
          <p>暂无连接设备</p>
          <el-button @click="goToHome" size="small">返回连接</el-button>
        </div>
      </div>
      
      <!-- 侧边栏底部按钮 -->
      <div class="sidebar-footer">
        <el-button
          @click="appStore.toggleTheme"
          :icon="appStore.isDarkMode ? Sunny : Moon"
          circle
          size="small"
          class="theme-btn"
        />
        <el-button
          @click="goToHome"
          :icon="ArrowLeft"
          size="small"
          class="back-btn"
        >
          返回
        </el-button>
        <!-- 移动端关闭侧边栏按钮 -->
        <el-button
          v-if="isMobile"
          @click="appStore.closeSidebar"
          :icon="Close"
          circle
          size="small"
          class="close-btn"
        />
      </div>
    </aside>

    <!-- 右侧聊天区域 -->
    <main class="chat-main">
      <!-- 顶部标题栏 -->
      <header class="chat-header">
        <div class="header-left">
          <!-- 移动端菜单按钮 -->
          <el-button
            v-if="isMobile"
            @click="appStore.toggleSidebar"
            :icon="Menu"
            size="small"
            circle
            class="menu-btn"
          />

          <!-- 移动端添加设备按钮 -->
          <el-button
            v-if="isMobile"
            @click="toggleAddDevicePanel"
            :icon="Plus"
            circle
            size="small"
            class="add-device-btn mobile"
            :class="{ 'active': showAddDevicePanel }"
            title="添加设备"
          />

          <!-- PC端添加设备按钮 -->
          <el-button
            v-if="!isMobile"
            @click="toggleAddDevicePanel"
            :icon="Plus"
            circle
            size="small"
            class="add-device-btn desktop"
            :class="{ 'active': showAddDevicePanel }"
            title="添加设备"
          />

          <!-- 当前设备信息 -->
          <div class="active-device-info">
            <el-icon size="24">
              <Monitor v-if="activeDevice?.type === 'desktop'" />
              <Iphone v-else />
            </el-icon>
            <span>{{ getActiveDeviceName() }}</span>
          </div>
        </div>
        
        <div class="header-right">
          <el-button 
            v-if="activeDevice"
            @click="disconnectDevice(activeDevice.id)"
            type="danger"
            size="small"
            :icon="Close"
          >
            断开
          </el-button>
        </div>
      </header>

      <!-- 聊天消息区域 -->
      <div 
        class="chat-messages" 
        ref="messagesContainer"
        @dragover.prevent="handleDragOver"
        @dragenter.prevent="handleDragEnter"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDropEvent"
        :class="{ 'drag-over': isDragOver }"
      >
        <!-- 拖拽提示层 -->
        <div v-if="isDragOver" class="drag-overlay">
          <div class="drag-hint">
            <el-icon><UploadFilled /></el-icon>
            <p>释放鼠标以上传文件</p>
            <span>支持多文件同时上传</span>
          </div>
        </div>

        <!-- 欢迎屏幕 - 仅在没有消息时显示 -->
        <div v-if="messageStore.currentMessages.length === 0" class="welcome-screen">
          <el-icon><ChatDotSquare /></el-icon>
          <p>开始发送文件或消息吧！</p>
          <span class="drag-tip">也可以直接拖拽文件到这里上传</span>
        </div>

        <!-- 消息列表 -->
        <div v-else class="messages-list">
          <template
            v-for="item in messagesWithTimeDividers"
            :key="item.id"
          >
            <!-- 时间分隔条 -->
            <TimeDivider
              v-if="item.type === 'time-divider'"
              :timestamp="item.timestamp"
            />

            <!-- 消息项 -->
            <div
              v-else
              class="message-item"
              :class="{ 'message-remote': item.isRemote, 'message-local': !item.isRemote }"
            >
              <!-- 文件消息 -->
              <FileMessage
                v-if="item.type === 'file'"
                :message="item"
                :is-remote="item.isRemote"
              />

              <!-- 文本消息 -->
              <div v-else-if="item.type === 'text'" class="file-message" :class="{ 'is-remote': item.isRemote, 'is-local': !item.isRemote }">
                <!-- 设备图标 -->
                <div class="device-icon">
                  <el-icon size="20" :color="item.isRemote ? '#909399' : '#14a19b'">
                    <component :is="getDeviceIconComponent(item.deviceType)" />
                  </el-icon>
                </div>

                <!-- 文本气泡 -->
                <div class="message-bubble text-bubble">
                  <div class="text-content">{{ item.content }}</div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 传输进度面板 - 隐藏，文件直接在聊天窗口中显示 -->
      <TransferProgress
        v-if="false"
        :active-transfers="activeTransfers"
        :all-transfers="allTransfers"
        :is-drag-over="false"
        :format-file-size="formatFileSize"
        :format-speed="formatSpeed"
        @clear-completed="clearCompletedTransfers"
        @remove-transfer="removeTransfer"
        @pause-transfer="pauseTransfer"
        @resume-transfer="resumeTransferById"
        @retry-transfer="retryTransfer"
      />

      <!-- 输入区域 -->
      <footer class="chat-input">
        <!-- 工具栏 - 五个彩色按钮，从左到右：图片、视频、音频、文件、表情 + 清除聊天记录 -->
        <div class="input-toolbar">
          <div class="file-tools">
            <!-- 图片按钮 #14a19b -->
            <button class="file-tool-button btn-image" title="图片" @click="selectFile('image')">
              <el-icon><Picture /></el-icon>
            </button>

            <!-- 视频按钮 #00b0f0 -->
            <button class="file-tool-button btn-video" title="视频" @click="selectFile('video')">
              <el-icon><VideoCamera /></el-icon>
            </button>

            <!-- 音频按钮 #ed7d31 -->
            <button class="file-tool-button btn-audio" title="音频" @click="selectFile('audio')">
              <el-icon><Microphone /></el-icon>
            </button>

            <!-- 文件按钮 #97361f -->
            <button class="file-tool-button btn-file" title="文件" @click="selectFile('file')">
              <el-icon><Document /></el-icon>
            </button>

            <!-- 表情按钮 #559fba -->
            <button class="file-tool-button btn-emoji" title="表情" @click="selectEmoji">
              <el-icon><ChatRound /></el-icon>
            </button>
          </div>

          <!-- 右侧功能按钮 -->
          <div class="toolbar-actions">
            <!-- 清除聊天记录按钮 -->
            <button class="file-tool-button btn-clear-history" title="清除聊天记录" @click="clearChatHistory">
              <el-icon><Delete /></el-icon>
            </button>
          </div>
        </div>
        
        <!-- 隐藏的文件输入框 - 为每种类型创建独立的输入框 -->
        <input 
          ref="imageInput" 
          type="file" 
          style="display: none" 
          accept="image/*"
          multiple
          @change="handleFileSelect"
        />
        <input 
          ref="videoInput" 
          type="file" 
          style="display: none" 
          accept="video/*"
          multiple
          @change="handleFileSelect"
        />
        <input 
          ref="audioInput" 
          type="file" 
          style="display: none" 
          accept="audio/*"
          multiple
          @change="handleFileSelect"
        />
        <input 
          ref="fileInput" 
          type="file" 
          style="display: none" 
          accept="*/*"
          multiple
          @change="handleFileSelect"
        />
        
        <!-- 文本输入框 -->
        <div class="text-input-area">
          <textarea
            v-model="textMessage"
            placeholder="请输入消息内容"
            class="message-input"
            rows="2"
            @keyup.ctrl.enter="sendTextMessage"
            @keyup.meta.enter="sendTextMessage"
          ></textarea>
          <button 
            class="send-btn"
            @click="sendTextMessage"
            :disabled="!textMessage.trim()"
          >
            <el-icon><Position /></el-icon>
          </button>
        </div>
        
        <div class="input-tips">
          <span>支持拖拽文件到聊天区域 | Ctrl+Enter快速发送</span>
        </div>
      </footer>
    </main>

    <!-- 添加设备面板 -->
    <div
      v-if="showAddDevicePanel"
      class="add-device-overlay"
      @click="closeAddDevicePanel"
    >
      <div class="add-device-container" @click.stop>
        <AddDevicePanel
          :pairing-code="deviceStore.pairingCode"
          :available-devices="deviceStore.availableDevices"
          :connecting-devices="connectingDevices"
          :current-device="deviceStore.currentDevice"
          :refreshing="refreshing"
          :discovering="deviceStore.discovering"
          :is-dark-mode="appStore.isDarkMode"
          @close="closeAddDevicePanel"
          @refresh-code="refreshPairingCode"
          @discover-devices="discoverDevices"
          @connect-by-code="connectByCode"
          @connect-to-device="connectToDevice"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor, Iphone, Cellphone, Connection, Close, Menu, ArrowLeft,
  Sunny, Moon, Picture, VideoCamera, Document, Delete,
  ChatDotSquare, Microphone, ChatRound, Position, UploadFilled, Plus
} from '@element-plus/icons-vue'
import { useDeviceStore, useWebSocketStore, useAppStore, useMessageStore } from '../store'
import { getEnhancedDeviceInfoSync } from '../utils/device'
import { useFileTransfer } from '../composables/useFileTransfer'
import { addTimeDividers } from '../utils/file'
import FileMessage from '../components/FileMessage.vue'
import TimeDivider from '../components/TimeDivider.vue'
import TransferProgress from '../components/TransferProgress.vue'
import AddDevicePanel from '../components/AddDevicePanel.vue'

// Store
const deviceStore = useDeviceStore()
const wsStore = useWebSocketStore()
const appStore = useAppStore()
const messageStore = useMessageStore()
const route = useRoute()
const router = useRouter()

// 响应式数据
const textMessage = ref('')
const messagesContainer = ref(null)
const isMobile = ref(window.innerWidth <= 768)
const imageInput = ref(null)
const videoInput = ref(null)
const audioInput = ref(null)
const fileInput = ref(null)
const showAddDevicePanel = ref(false)
const refreshing = ref(false)
const connectingDevices = ref(new Set())

// 文件传输增强功能
const {
  // 拖拽上传
  isDragOver,
  handleDragOver,
  handleDragLeave,
  handleDragEnter,
  handleDrop,
  
  // 传输进度
  transferProgress,
  formatFileSize,
  formatSpeed,
  
  // 批量传输
  uploadFiles,
  
  // 计算属性
  activeTransfers,
  allTransfers,
  
  // 工具函数
  clearCompletedTransfers
} = useFileTransfer()

// 计算属性
const activeDevice = computed(() => {
  return deviceStore.connectedDevices.find(device => device.id === messageStore.activeDeviceId)
})

const uploadUrl = computed(() => {
  const baseUrl = getApiBaseUrl()
  return `${baseUrl}/api/file/upload`
})

// 带时间分隔条的消息列表
const messagesWithTimeDividers = computed(() => {
  return addTimeDividers(messageStore.currentMessages, 5) // 5分钟间隔显示时间
})

// 获取API基础URL
const getApiBaseUrl = () => {
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:8080'
  }
  return `http://${window.location.hostname}:8080`
}

// 响应式处理
const handleResize = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    appStore.closeSidebar()
  }
}

// 选择设备
const selectDevice = (device) => {
  messageStore.setActiveDevice(device.id)
  if (isMobile.value) {
    appStore.closeSidebar()
  }
}

// 断开设备连接
const disconnectDevice = async (deviceId) => {
  try {
    await ElMessageBox.confirm('确定要断开连接吗？', '确认断开', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    wsStore.send({
      type: 'disconnect',
      data: { device_id: deviceId }
    })
    
    deviceStore.removeConnectedDevice(deviceId)
    messageStore.clearMessages(deviceId)
    
    // 如果是当前活动设备，清除活动状态
    if (messageStore.activeDeviceId === deviceId) {
      const remainingDevices = deviceStore.connectedDevices
      if (remainingDevices.length > 0) {
        messageStore.setActiveDevice(remainingDevices[0].id)
      } else {
        messageStore.setActiveDevice('')
        router.push('/')
      }
    }
    
    ElMessage.success('已断开连接')
  } catch {
    // 用户取消
  }
}

// 返回首页
const goToHome = () => {
  router.push('/')
}

// 添加设备面板控制
const toggleAddDevicePanel = () => {
  showAddDevicePanel.value = !showAddDevicePanel.value
  if (showAddDevicePanel.value) {
    // 打开面板时自动发现设备
    discoverDevices()
  }
}

const closeAddDevicePanel = () => {
  showAddDevicePanel.value = false
}

// 刷新配对码
const refreshPairingCode = async () => {
  refreshing.value = true

  try {
    // 重新连接WebSocket获取新的配对码
    wsStore.disconnect()
    await initWebSocketConnection()

    ElMessage.success('配对码已刷新')
  } catch (error) {
    ElMessage.error('刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}

// 发现设备
const discoverDevices = () => {
  deviceStore.discovering = true

  wsStore.send({
    type: 'discover_devices',
    data: {}
  })

  setTimeout(() => {
    deviceStore.discovering = false
  }, 2000)
}

// 通过配对码连接
const connectByCode = async (code) => {
  try {
    const success = wsStore.send({
      type: 'connect_by_code',
      data: {
        code: code
      }
    })

    if (!success) {
      throw new Error('WebSocket连接未建立')
    }

    ElMessage.info('正在连接...')

  } catch (error) {
    ElMessage.error('连接失败：' + error.message)
  }
}

// 连接到设备
const connectToDevice = async (device) => {
  connectingDevices.value.add(device.id)

  try {
    const success = wsStore.send({
      type: 'connect_request',
      data: {
        target_device: device.id,
        source_device: deviceStore.currentDevice
      }
    })

    if (!success) {
      throw new Error('WebSocket连接未建立')
    }

    ElMessage.info(`正在连接到 ${device.name}...`)

  } catch (error) {
    ElMessage.error('连接失败：' + error.message)
    connectingDevices.value.delete(device.id)
  }
}

// 🗑️ 清除聊天历史记录
const clearChatHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除当前设备的聊天记录吗？此操作不可恢复。',
      '清除聊天记录',
      {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 清除当前活动设备的消息
    if (messageStore.activeDeviceId) {
      messageStore.clearMessagesForIP(messageStore.activeDeviceId)
      ElMessage.success('聊天记录已清除')
    }
  } catch {
    // 用户取消
  }
}

// 获取设备类型
const getDeviceType = (deviceId, isRemote = false) => {
  if (isRemote) {
    // 远程设备，从已连接设备中查找
    const device = deviceStore.connectedDevices.find(d => d.id === deviceId)
    return device?.type || 'desktop'
  } else {
    // 本地设备
    return deviceStore.currentDevice?.type || 'desktop'
  }
}

// 获取设备图标组件
const getDeviceIconComponent = (deviceType) => {
  switch (deviceType) {
    case 'mobile':
    case 'phone':
    case 'android':
    case 'ios':
      return 'Iphone'
    case 'tablet':
    case 'ipad':
    case 'android_tablet':
      return 'Cellphone'
    case 'desktop':
    case 'pc':
    case 'computer':
    case 'laptop':
    case 'windows':
    case 'macos':
    case 'linux':
    default:
      return 'Monitor'
  }
}

// 发送文本消息
const sendTextMessage = async () => {
  if (!textMessage.value.trim() || !activeDevice.value) return

  const message = {
    type: 'text',
    content: textMessage.value,
    isRemote: false,
    deviceId: activeDevice.value.id,
    deviceType: getDeviceType(activeDevice.value.id, false) // 添加设备类型
  }

  messageStore.addMessage(message)

  const sendSuccess = wsStore.send({
    type: 'text_message',
    data: {
      content: textMessage.value,
      target_device: activeDevice.value.id,
      timestamp: new Date().toISOString()
    }
  })

  if (!sendSuccess) {
    ElMessage.error('消息发送失败，正在尝试重连...')

    // 尝试重连
    try {
      await wsStore.reconnect()

      // 重新发送消息
      const retrySuccess = wsStore.send({
        type: 'text_message',
        data: {
          content: textMessage.value,
          target_device: activeDevice.value.id,
          timestamp: new Date().toISOString()
        }
      })

      if (!retrySuccess) {
        ElMessage.error('重连后仍无法发送消息')
        return
      }

      ElMessage.success('重连成功，消息已发送')
    } catch (error) {
      ElMessage.error('重连失败，请检查网络连接')
      return
    }
  }

  textMessage.value = ''
  scrollToBottom()
}

// 文件类型选择

const selectFile = (fileType) => {
  if (!activeDevice.value) {
    ElMessage.error('请先选择设备')
    return
  }
  
  // 根据文件类型选择对应的文件输入框
  let inputRef = null
  switch (fileType) {
    case 'image':
      inputRef = imageInput.value
      break
    case 'video':
      inputRef = videoInput.value
      break
    case 'audio':
      inputRef = audioInput.value
      break
    case 'file':
      inputRef = fileInput.value
      break
    default:
      inputRef = fileInput.value
  }
  
  // 直接触发对应的文件选择器
  if (inputRef) {
    inputRef.click()
  }
}

// 处理文件选择 (支持多文件)
const handleFileSelect = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return
  
  // 批量上传文件
  await handleBatchUpload(files)
  
  // 清空文件输入框
  event.target.value = ''
}

// 批量上传处理
const handleBatchUpload = async (files) => {
  if (!activeDevice.value) {
    ElMessage.error('请先选择设备')
    return
  }

  const maxSize = deviceStore.isLanMode ? 20 * 1024 * 1024 * 1024 : 5 * 1024 * 1024 * 1024

  await uploadFiles(files, {
    maxFileSize: maxSize,
    uploadUrl: uploadUrl.value,
    onSuccess: (result, file) => {
      // 添加发送方的消息到聊天记录
      const message = {
        type: 'file',
        file: result.data,
        isRemote: false, // 👈 发送方显示为本地消息
        deviceId: activeDevice.value.id,
        timestamp: Date.now(),
        deviceType: getDeviceType(activeDevice.value.id, false)
      }
      messageStore.addMessage(message)

      // 通过WebSocket通知对方设备
      sendFileNotification(result.data, file)

      ElMessage.success(`文件 "${file.name}" 发送成功`)
      scrollToBottom()
    },
    onError: (error, file) => {
      ElMessage.error(`文件 "${file.name}" 发送失败: ${error.message}`)
    },
    // 👈 移除messageStore和activeDevice参数，避免在useFileTransfer中重复添加消息
    skipMessageAdd: true
  })
}

// 传输控制函数
const removeTransfer = (fileId) => {
  transferProgress.delete(fileId)
}

const pauseTransfer = (fileId) => {
  // TODO: 实现暂停传输
  ElMessage.info('暂停功能开发中')
}

const resumeTransferById = (fileId) => {
  // TODO: 实现恢复传输
  ElMessage.info('恢复功能开发中')
}

const retryTransfer = (fileId) => {
  // TODO: 实现重试传输
  ElMessage.info('重试功能开发中')
}

// 表情选择
const selectEmoji = () => {
  // 简单的表情选择器
  const emojis = ['😀', '😊', '😍', '🤔', '😂', '👍', '❤️', '🎉', '💯', '🔥']
  
  ElMessageBox.prompt('选择表情或输入自定义表情', '表情', {
    confirmButtonText: '发送',
    cancelButtonText: '取消',
    inputValue: emojis[0],
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const emoji = instance.inputValue.trim()
        if (emoji) {
          // 直接将表情作为文本消息发送
          const tempMessage = textMessage.value
          textMessage.value = emoji
          sendTextMessage()
          textMessage.value = tempMessage
        }
      }
      done()
    }
  }).catch(() => {
    // 用户取消
  })
}

// 文件上传处理
const beforeUpload = (file) => {
  if (!activeDevice.value) {
    ElMessage.error('请先选择设备')
    return false
  }
  
  // 检查文件大小限制
  const maxSize = deviceStore.isLanMode ? 20 * 1024 * 1024 * 1024 : 5 * 1024 * 1024 * 1024
  if (file.size > maxSize) {
    const limit = deviceStore.isLanMode ? '20GB' : '5GB'
    ElMessage.error(`文件大小超过限制 (${limit})`)
    return false
  }
  
  return true
}

const onUploadSuccess = (response) => {
  if (response.success && activeDevice.value) {
    const message = {
      type: 'file',
      file: response.data,
      isRemote: false,
      deviceId: activeDevice.value.id,
      deviceType: getDeviceType(activeDevice.value.id, false) // 添加设备类型
    }
    messageStore.addMessage(message)
    scrollToBottom()
    ElMessage.success('文件发送成功')
  } else {
    ElMessage.error('文件发送失败')
  }
}

const onUploadError = (error) => {
  ElMessage.error('文件发送失败')
}

const onUploadProgress = (event, file) => {
  // 上传进度处理（如需要可在此添加进度显示逻辑）
}

// 文件下载
const handleDownload = (file) => {
  const link = document.createElement('a')
  link.href = `${getApiBaseUrl()}/api/file/download/${file.id}`
  link.download = file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 清空消息
const clearMessages = async () => {
  if (!activeDevice.value) return
  
  try {
    await ElMessageBox.confirm('确定要清空聊天记录吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    messageStore.clearMessages(activeDevice.value.id)
    ElMessage.success('聊天记录已清空')
  } catch {
    // 用户取消
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 未读消息处理（预留功能）
const hasUnreadMessages = (deviceId) => {
  // TODO: 实现未读消息逻辑
  return false
}

const getUnreadCount = (deviceId) => {
  // TODO: 实现未读消息计数
  return 0
}

// WebSocket消息处理
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'device_registered':
      // 设备注册成功，更新配对码
      if (message.data.pairing_code) {
        deviceStore.pairingCode = message.data.pairing_code
        console.log('TransferPage: 配对码已更新:', message.data.pairing_code)
      }
      if (message.data.device_name) {
        deviceStore.updateCurrentDevice({
          ...deviceStore.currentDevice,
          name: message.data.device_name,
          type: message.data.device_type || deviceStore.currentDevice.type
        })
      }
      break

    case 'new_file':
      if (message.data.file) {
        const fileMessage = {
          type: 'file',
          file: message.data.file,
          isRemote: true,
          deviceId: message.data.from_device || messageStore.activeDeviceId,
          deviceType: getDeviceType(message.data.from_device || messageStore.activeDeviceId, true) // 添加设备类型
        }
        messageStore.addMessage(fileMessage)
        scrollToBottom()
      }
      break

    case 'new_text_message':
      if (message.data.content) {
        const textMsg = {
          type: 'text',
          content: message.data.content,
          isRemote: true,
          deviceId: message.data.from_device_id || messageStore.activeDeviceId,  // 使用from_device_id
          timestamp: message.data.timestamp,
          deviceType: getDeviceType(message.data.from_device_id || messageStore.activeDeviceId, true) // 添加设备类型
        }
        messageStore.addMessage(textMsg)
        scrollToBottom()
      }
      break

    case 'text_message': // 可能的另一种消息类型
      if (message.data.content) {
        const textMsg = {
          type: 'text',
          content: message.data.content,
          isRemote: true,
          deviceId: message.data.from_device || message.data.source_device || messageStore.activeDeviceId,
          deviceType: getDeviceType(message.data.from_device || message.data.source_device || messageStore.activeDeviceId, true) // 添加设备类型
        }
        messageStore.addMessage(textMsg)
        scrollToBottom()
      }
      break
      
    case 'connection_established':
      // 清除连接状态
      connectingDevices.value.clear()

      // 关闭添加设备面板
      closeAddDevicePanel()

      // 添加到已连接设备
      deviceStore.addConnectedDevice(message.data)

      // 设置活动设备为新连接的设备
      messageStore.setActiveDevice(message.data.id)

      // 如果当前路由的设备ID与新连接的设备ID不同，更新路由
      if (route.params.deviceId !== message.data.id) {
        router.replace(`/transfer/${message.data.id}`)
      }

      ElMessage.success(`已连接到 ${message.data.name}`)
      break

    case 'file_notification':
      // 只有当前活动设备匹配发送方时才添加消息
      if (message.data.from_device_id && messageStore.activeDeviceId === message.data.from_device_id) {
        const fileMessage = {
          type: 'file',
          file: message.data.file,
          isRemote: true,
          deviceId: message.data.from_device_id, // 👈 使用发送方设备ID
          timestamp: message.data.timestamp,
          senderName: message.data.senderName,
          deviceType: getDeviceType(message.data.from_device_id, true)
        }
        messageStore.addMessage(fileMessage)

        // 显示通知
        ElMessage.success(`收到来自 ${message.data.senderName} 的文件: ${message.data.file.name}`)
        scrollToBottom()
      }
      break

    case 'device_disconnected':
      deviceStore.removeConnectedDevice(message.data.device_id)
      if (messageStore.activeDeviceId === message.data.device_id) {
        const remainingDevices = deviceStore.connectedDevices
        if (remainingDevices.length > 0) {
          messageStore.setActiveDevice(remainingDevices[0].id)
        } else {
          router.push('/')
        }
      }
      break

    case 'error':
      if (message.data.type === 'device_offline') {
        ElMessage.error('设备已离线，请重新连接')
        // 可以在这里添加自动重连逻辑
        if (message.data.action === 'reconnect_required') {
          // 跳转回首页重新连接
          setTimeout(() => {
            router.push('/')
          }, 2000)
        }
      } else {
        ElMessage.error(message.data.message || '发生错误')
      }
      break
      
    default:
      // 尝试处理可能的文本消息
      if (message.data && (message.data.content || message.data.text || message.data.message)) {
        const content = message.data.content || message.data.text || message.data.message
        const textMsg = {
          type: 'text',
          content: content,
          isRemote: true,
          deviceId: message.data.from_device || message.data.source_device || messageStore.activeDeviceId,
          deviceType: getDeviceType(message.data.from_device || message.data.source_device || messageStore.activeDeviceId, true) // 添加设备类型
        }
        messageStore.addMessage(textMsg)
        scrollToBottom()
      }
      break
  }
}

// 监听路由参数变化
watch(() => route.params.deviceId, (newDeviceId) => {
  if (newDeviceId) {
    messageStore.setActiveDevice(newDeviceId)
  }
}, { immediate: true })

// 获取设备显示名称 - 与主页保持一致
const getDeviceDisplayName = (device) => {
  if (!device) return '未知设备'

  // 如果设备对象有详细信息，直接使用
  if (device.model_name) return device.model_name
  if (device.deviceName) return device.deviceName
  if (device.name) return device.name

  // 使用同步版本的设备检测器
  const deviceInfo = getEnhancedDeviceInfoSync()
  return deviceInfo.deviceName
}

// 获取当前活动设备名称
const getActiveDeviceName = () => {
  return getDeviceDisplayName(activeDevice.value)
}

// 发送文件通知给对方设备
const sendFileNotification = (fileData, originalFile) => {
  if (!activeDevice.value) {
    return
  }

  const notification = {
    type: 'file_notification',
    data: {
      target_device: activeDevice.value.id, // 👈 添加目标设备ID
      file: {
        id: fileData.id,
        name: fileData.name || originalFile.name,
        size: fileData.size || originalFile.size,
        path: fileData.path
      },
      senderName: deviceStore.currentDevice?.name || 'Unknown Device',
      timestamp: Date.now()
    }
  }

  const success = wsStore.send(notification)
  if (!success) {
    ElMessage.error('文件通知发送失败，请检查网络连接')
  }
}

// 处理拖拽事件
const handleDropEvent = async (event) => {
  // 如果 activeDevice 为空，等待一小段时间再试（处理响应性时机问题）
  if (!activeDevice.value && messageStore.activeDeviceId) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  handleDrop(event, activeDevice.value, handleBatchUpload);
}

// 格式化时间显示
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 如果是昨天或更早
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// WebSocket连接初始化
const initWebSocketConnection = async () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  let wsHost = window.location.hostname
  if (wsHost === 'localhost' || wsHost === '127.0.0.1') {
    wsHost = 'localhost'
  }
  const wsUrl = `${protocol}//${wsHost}:8080/api/ws`

  try {
    await wsStore.connect(wsUrl)
  } catch (error) {
    ElMessage.error('网络连接失败，请检查网络设置')
  }
}

// 重新注册设备信息
const reRegisterDevice = async () => {
  // 使用同步版本的设备检测
  const enhancedDeviceInfo = getEnhancedDeviceInfoSync()

  // 发送设备注册信息
  const success = wsStore.send({
    type: 'device_info',
    data: {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      deviceName: deviceStore.currentDevice?.name || enhancedDeviceInfo.deviceName,
      deviceType: deviceStore.currentDevice?.type || enhancedDeviceInfo.deviceType,
      systemName: enhancedDeviceInfo.systemName,
      systemVersion: enhancedDeviceInfo.systemVersion,
      manufacturer: enhancedDeviceInfo.manufacturer,
      model: enhancedDeviceInfo.model
    }
  })

  if (!success) {
    ElMessage.error('设备注册失败，请检查网络连接')
  }
}

// 🔄 恢复持久化状态
const restorePersistedState = async () => {
  try {
    // 恢复连接状态
    const hasConnectionState = deviceStore.loadConnectionState()

    // 恢复消息历史
    messageStore.loadMessagesFromStorage()

    // 恢复活动设备
    messageStore.loadActiveDeviceFromStorage()

    console.log('状态恢复完成:', {
      hasConnectionState,
      connectedDevices: deviceStore.connectedDevices.length,
      totalMessages: messageStore.messages.length,
      activeDevice: messageStore.activeDeviceId
    })

    return hasConnectionState
  } catch (error) {
    console.error('恢复状态失败:', error)
    return false
  }
}

// 生命周期
onMounted(async () => {
  // 🔄 首先恢复持久化状态
  await restorePersistedState()

  // 设置WebSocket消息处理器
  wsStore.onMessage(handleWebSocketMessage)

  // 添加窗口大小监听
  window.addEventListener('resize', handleResize)

  // 如果WebSocket未连接，尝试重新连接
  if (!wsStore.isConnected) {
    initWebSocketConnection()
  }

  // 如果有路由参数中的设备ID，但没有在连接列表中，可能是页面跳转时状态丢失
  if (route.params.deviceId && deviceStore.connectedDevices.length === 0) {
    // 创建一个临时的设备对象，等待真正的连接消息
    const tempDevice = {
      id: route.params.deviceId,
      name: '连接中的设备',
      type: 'unknown',
      ip: '',
      platform: '',
      online: true
    }

    deviceStore.addConnectedDevice(tempDevice)

    // 如果WebSocket已连接，等待一段时间接收真正的连接消息
    if (wsStore.isConnected) {
      setTimeout(() => {
        // 如果设备信息还是临时的，尝试重新注册
        const currentDevice = deviceStore.connectedDevices.find(d => d.id === route.params.deviceId)
        if (currentDevice && currentDevice.name === '连接中的设备') {
          reRegisterDevice()
        }
      }, 2000)
    } else {
      initWebSocketConnection()
    }
  }

  // 如果没有连接的设备，返回首页
  if (deviceStore.connectedDevices.length === 0) {
    router.push('/')
    return
  }

  // 设置默认活动设备
  if (!messageStore.activeDeviceId && deviceStore.connectedDevices.length > 0) {
    messageStore.setActiveDevice(deviceStore.connectedDevices[0].id)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '../assets/styles/transfer-new.css';
@import '../assets/styles/file-message.css';
@import '../assets/styles/media-message.css';

/* 拖拽样式 */
.chat-messages {
  position: relative;
  transition: all 0.2s ease;
}

.chat-messages.drag-over {
  background: rgba(64, 158, 255, 0.05);
  border: 2px dashed var(--el-color-primary);
  transform: scale(0.98);
}

/* 移除样式覆盖，使用file-message.css中的样式 */

/* 文本消息样式已在file-message.css中定义 */

/* 确保FileMessage组件样式正确应用 */
.file-message {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
}

/* 确保图片消息直接显示 */
.image-message {
  max-width: 240px !important;
  min-width: 120px !important;
  position: relative !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

/* 确保视频消息直接显示 */
.video-message {
  max-width: 280px !important;
  min-width: 160px !important;
  position: relative !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  background: #000 !important;
}

/* 确保其他文件使用气泡样式 */
.message-bubble.file-bubble {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 16px !important;
  padding: 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  transition: all 0.3s ease !important;
  max-width: 400px !important;
  min-width: 280px !important;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.15);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 8px;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.drag-hint {
  text-align: center;
  color: var(--el-color-primary);
  padding: 32px;
}

.drag-hint .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.drag-hint p {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px;
}

.drag-hint span {
  font-size: 14px;
  opacity: 0.8;
}

.drag-tip {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  opacity: 0.7;
}

/* 添加设备面板样式 */
.add-device-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease;
}

.add-device-container {
  position: relative;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 添加设备按钮激活状态 */
.add-device-btn.active {
  background: var(--el-color-primary) !important;
  color: white !important;
  transform: scale(1.1);
}

/* 移动端面板定位调整 */
@media (max-width: 768px) {
  .add-device-overlay {
    padding: 20px;
  }

  .add-device-container {
    width: 100%;
    max-width: 320px;
  }
}
</style> 