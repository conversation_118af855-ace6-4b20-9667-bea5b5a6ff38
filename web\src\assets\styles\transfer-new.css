/* 新版文件传输页样式 - 根据设计规范 */

.transfer-page {
  height: 100vh;
  width: 100vw; /* 确保不超过视口宽度 */
  display: flex;
  position: relative;
  overflow: hidden; /* 防止整个页面溢出 */
  gap: 6px; /* 左右两个板块之间的间距 */
  padding: 0; /* 移除默认padding */
  box-sizing: border-box;
}

/* 暗色主题下的文件发送界面背景 */
.transfer-page.dark-theme {
  background-image: linear-gradient(to bottom, #2b343e, #2d3640, #303843, #323b45, #353d48, #353d48, #353d48, #353d48, #323b45, #303843, #2d3640, #2b343e); /* 暗黑模式渐变背景 */
}

/* 亮色主题背景 */
.transfer-page.light-theme {
  background: linear-gradient(to top, #f3e7e9 0%, #e3eeff 99%, #e3eeff 100%); /* 亮色主题渐变背景 */
}

/* 移动端遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: block;
}

/* 左侧设备栏 */
.device-sidebar {
  width: 280px;
  background: rgba(0, 0, 0, 0.3); /* 暗黑模式左侧菜单栏颜色填充 */
  border: none; /* 移除边框 */
  border-radius: 8px; /* 添加圆角 */
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
  transition: transform 0.3s ease;
  margin: 8px 0 8px 8px; /* 添加边距，确保不贴边 */
}

/* 亮色主题下的左侧栏 */
.transfer-page.light-theme .device-sidebar {
  background: rgba(255, 255, 255, 0.3); /* 亮色主题下30%白色背景 */
  border: 1px solid #e5e7eb;
}

/* 移动端侧边栏 */
.device-sidebar.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  transform: translateX(-100%);
  z-index: 1001;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.transfer-page.sidebar-open .device-sidebar.mobile-sidebar {
  transform: translateX(0);
}

/* 侧边栏Logo */
.sidebar-header {
  padding: 10px ;
  margin: 10px 10px 10px 50px;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo img {
  width: 48px;
  height: 48px;
}

.sidebar-logo span {
  font-size: 28px;
  font-weight: 600;
  color: #14a19b;
}

/* 分割线 */
.sidebar-divider {
  height: 1px;
  background: #3c3c3c; /* 暗色主题分割线颜色 */
  margin: 10px 20px;
}

/* 亮色主题下的分割线 */
.transfer-page.light-theme .sidebar-divider {
  background: #818383; /* 亮色主题分割线颜色 */
}

/* 已连接设备标题 */
.connected-devices-title {
  padding: 16px 20px;
  font-size: 20px;
  font-weight: 600;
  color: #14a19b; /* 固定使用主题绿色 */
  text-align: center;
}

/* 设备列表 */
.connected-devices {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  width: 100%; /* 确保容器宽度 */
  box-sizing: border-box; /* 包含padding在宽度内 */
  overflow: hidden; /* 防止内容溢出 */
}

.device-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.device-item.active {
  background: rgba(20, 161, 155, 0.15);
  border-color: var(--primary-color);
}

.device-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 亮色主题下的设备头像 */
.transfer-page.light-theme .device-avatar {
  background: #f3f4f6; /* 亮色主题下使用浅灰色背景 */
  color: #1f2937; /* 亮色主题下使用深色图标 */
}

/* 设备头像中的图标颜色 */
.device-avatar .el-icon {
  color: inherit; /* 继承父元素颜色 */
}

.device-info {
  flex: 1;
  min-width: 0; /* 允许flex项目缩小到内容以下 */
  overflow: hidden; /* 防止内容溢出 */
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 防止换行 */
  max-width: 100%; /* 确保不超过父容器 */
}

.device-status {
  font-size: 12px;
  color: var(--text-secondary);
}

.disconnect-action {
  background: rgb(244, 107, 107); /* 👈 默认红色背景 */
  border: 1px solid #f27575; /* 👈 默认红色边框 */
  color: #f3f2f2; /* 👈 默认红色文字 */
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0; /* 防止按钮缩小 */
  white-space: nowrap; /* 防止文字换行 */
  min-width: 50px; /* 确保按钮最小宽度 */
  text-align: center; /* 文字居中 */
}

.disconnect-action:hover {
  background: rgba(239, 68, 68, 0.2); /* 👈 悬停时更深的红色背景 */
  border-color: #dc2626; /* 👈 悬停时更深的红色边框 */
  color: #dc2626; /* 👈 悬停时更深的红色文字 */
  transform: scale(1.02); /* 👈 悬停时轻微放大效果 */
}

/* 空设备状态 */
.empty-devices {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.empty-devices .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-devices p {
  margin-bottom: 16px;
  font-size: 14px;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 24px 20px; /* 增加上下内边距 */
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 16px; /* 增加按钮间距 */
  align-items: center;
  justify-content: center; /* 按钮居中显示 */
  min-height: 60px; /* 确保容器有足够高度 */
}

/* 移动端侧边栏底部特殊处理 */
@media (max-width: 768px) {
  .sidebar-footer {
    padding: 20px 16px !important;
    min-height: 60px !important;
    gap: 12px !important;
    justify-content: space-around !important;
  }

  .sidebar-footer .el-button {
    width: 36px !important;
    height: 36px !important;
    min-height: 36px !important;
    max-height: 36px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
  }
}

/* PC端主题切换按钮 */
.sidebar-footer .theme-btn {
  background: rgba(20, 161, 155, 0.15) !important; /* 主题绿色底色 */
  border-color: rgba(20, 161, 155, 0.3) !important;
  color: #14a19b !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  border-radius: 50% !important; /* 圆形按钮 */
  min-width: unset !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* PC端返回按钮 */
.sidebar-footer .back-btn {
  background: rgba(59, 130, 246, 0.15) !important; /* 蓝色底色 */
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: #3b82f6 !important;
  padding: 10px 16px !important;
  font-size: 16px !important;
  border-radius: 8px !important;
  min-height: 40px !important;
  min-width: 80px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* PC端按钮悬停效果 */
.sidebar-footer .theme-btn:hover {
  background: rgba(20, 161, 155, 0.25) !important;
  transform: scale(1.05); /* 悬停时轻微放大效果 */
}

.sidebar-footer .back-btn:hover {
  background: rgba(59, 130, 246, 0.25) !important;
  transform: translateY(-1px); /* 悬停时轻微上移效果 */
}

/* 主题切换按钮特殊样式 - 保持圆形 */
.sidebar-footer .theme-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  border-radius: 50% !important; /* 圆形按钮 */
  min-width: unset !important;
}



/* 返回按钮保持矩形 */
.sidebar-footer .back-btn {
  min-width: 80px !important;
}

/* 按钮图标大小 */
.sidebar-footer .theme-btn .el-icon,
.sidebar-footer .back-btn .el-icon,
.sidebar-footer .close-btn .el-icon {
  font-size: 18px !important; /* 增大图标尺寸 */
}

/* 关闭侧边栏按钮样式 */
.sidebar-footer .close-btn {
  background: rgba(239, 68, 68, 0.15) !important; /* 红色底色 */
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sidebar-footer .close-btn:hover {
  background: rgba(239, 68, 68, 0.25) !important;
  transform: scale(1.05);
}

.sidebar-close-btn {
  background: rgba(239, 68, 68, 0.1); /* 👈 默认红色背景 */
  border: 1px solid #ef4444; /* 👈 默认红色边框 */
  color: #ef4444; /* 👈 默认红色文字 */
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: auto;
  transition: all 0.2s ease; /* 👈 添加过渡效果 */
}

.sidebar-close-btn:hover {
  background: rgba(239, 68, 68, 0.2); /* 👈 悬停时更深的红色背景 */
  border-color: #dc2626; /* 👈 悬停时更深的红色边框 */
  color: #dc2626; /* 👈 悬停时更深的红色文字 */
  transform: scale(1.02); /* 👈 悬停时轻微放大效果 */
}

/* 右侧聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.4); /* 暗黑模式右侧聊天窗口颜色填充 */
  border-radius: 8px; /* 添加圆角 */
  position: relative;
  margin: 8px 8px 8px 0; /* 添加边距，确保不贴边，左侧无边距因为有gap */
  min-width: 0; /* 允许flex项目缩小 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 亮色主题下的聊天区域 */
.transfer-page.light-theme .chat-main {
  background: rgba(255, 255, 255, 0.6); /* 亮色主题下95%白色背景 */
}

/* 顶部标题栏 */
.chat-header {
  padding: 16px 20px;
  background: transparent; /* 透明背景，融入右侧板块 */
  border-bottom: none; /* 移除原分割线 */
  border-radius: 8px 8px 0 0; /* 顶部圆角 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  position: relative; /* 为阴影定位做准备 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5); /* 添加向下的阴影效果 */
}

/* 亮色主题下的顶部标题栏 */
.transfer-page.light-theme .chat-header {
  background: transparent; /* 亮色主题下也使用透明背景 */
  border-bottom: none; /* 移除分割线 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 亮色主题下的阴影效果，稍微淡一些 */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1; /* 占据剩余空间 */
}

.menu-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(20, 161, 155, 0.3) !important;
  color: var(--primary-color) !important;
}

.menu-btn:hover {
  background: rgba(20, 161, 155, 0.2) !important;
}

/* 添加设备按钮 */
.add-device-btn {
  background: rgba(32, 201, 151, 0.1) !important; /* 绿色背景 */
  border-color: rgba(32, 201, 151, 0.3) !important;
  color: #20c997 !important;
  transition: all 0.2s ease;
}

.add-device-btn:hover {
  background: rgba(32, 201, 151, 0.2) !important;
  border-color: #20c997 !important;
  color: #1ba085 !important;
  transform: scale(1.05);
}

/* PC端添加设备按钮位置 */
.add-device-btn.desktop {
  margin-right: 12px;
}

/* 移动端添加设备按钮位置 */
.add-device-btn.mobile {
  margin-right: 12px;
}

.active-device-info {
  display: flex;
  align-items: center;
  justify-content: center; /* 水平居中 */
  gap: 12px;
  flex: 1; /* 占据剩余空间，实现居中效果 */
  text-align: center; /* 文本居中 */
}

.active-device-info .el-icon {
  color: #14a19b; /* 使用主题绿色 */
}

.active-device-info span {
  font-size: 20px; /* 增大字体 */
  font-weight: 700; /* 加粗字体 */
  color: #ffffff; /* 白色文字，在暗色背景下更清晰 */
  letter-spacing: 0.5px; /* 增加字母间距 */
}

/* 亮色主题下的设备名称 */
.transfer-page.light-theme .active-device-info span {
  color: #1f2937; /* 亮色主题下使用深色文字 */
}

/* 右侧按钮区域 */
.header-right {
  display: flex;
  align-items: center;
  min-width: 80px; /* 确保右侧有固定宽度，保持居中平衡 */
  justify-content: flex-end;
}

.header-right .el-button {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: #ef4444 !important;
  color: #ef4444 !important;
}

.header-right .el-button:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: #dc2626 !important; /* 👈 悬停时更深的红色边框 */
  color: #dc2626 !important; /* 👈 悬停时更深的红色文字 */
  transform: scale(1.02); /* 👈 悬停时轻微放大效果 */
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: transparent; /* 透明背景，融入右侧板块 */
  margin: 0; /* 移除外边距 */
}

/* 亮色主题下的消息区域 */
.transfer-page.light-theme .chat-messages {
  background: transparent; /* 亮色主题下也使用透明背景 */
}

.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 聊天样式已移至 chat-bubbles.css */

.welcome-screen .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.welcome-screen p {
  font-size: 16px;
}

/* 输入区域 */
.chat-input {
  padding: 20px;
  background: transparent; /* 透明背景，融入右侧板块 */
  border-top: 1px solid rgba(255, 255, 255, 0.1); /* 半透明分割线 */
  border-radius: 0 0 8px 8px; /* 底部圆角 */
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 亮色主题下的输入区域 */
.transfer-page.light-theme .chat-input {
  background: transparent; /* 亮色主题下也使用透明背景 */
  border-top: 1px solid rgba(0, 0, 0, 0.1); /* 亮色主题下的分割线 */
}

/* 工具栏 */
.input-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐 */
  margin-bottom: 12px;
}

.file-tools {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 工具栏右侧功能按钮区域 */
.toolbar-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 五个彩色按钮样式 */
.file-tool-button {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.file-tool-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 五个按钮的具体颜色 */
.btn-image {
  background: #14a19b; /* 图片按钮 */
}

.btn-video {
  background: #00b0f0; /* 视频按钮 */
}

.btn-audio {
  background: #ed7d31; /* 音频按钮 */
}

.btn-file {
  background: #97361f; /* 文件按钮 */
}

.btn-emoji {
  background: #559fba; /* 表情按钮 */
}

/* 清除聊天记录按钮 */
.btn-clear-history {
  background: rgba(239, 68, 68, 0.1) !important; /* 红色背景 */
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

.btn-clear-history:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: #dc2626 !important;
  color: #dc2626 !important;
  transform: scale(1.05);
}

/* 文本输入区域 - 改为相对定位容器 */
.text-input-area {
  position: relative;
  display: block;
}

.message-input {
  width: 100%;
  background: rgba(14, 14, 14, 0.3); /* 暗色主题下输入框背景色，增加透明度 */
  border: 1px solid rgba(107, 114, 128, 0.5); /* 半透明边框 */
  border-radius: 12px;
  padding: 16px 80px 16px 16px; /* 右侧留出80px空间给发送按钮 */
  font-size: 14px;
  color: #f9fafb; /* 暗色主题下文字颜色 */
  resize: none;
  min-height: 120px;
  max-height: 200px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px); /* 添加背景模糊效果 */
  box-sizing: border-box;
}

/* 亮色主题下的输入框 */
.transfer-page.light-theme .message-input {
  background: rgba(255, 255, 255, 0.9); /* 亮色主题下90%白色背景 */
  border: 1px solid rgba(209, 213, 219, 0.5); /* 半透明边框 */
  color: #374151; /* 亮色主题下文字颜色 */
}

.message-input:focus {
  outline: none;
  border-color: #0f3432; /* 焦点时边框颜色 */
  box-shadow: 0 0 0 2px rgba(20, 161, 155, 0.2);
}

.message-input::placeholder {
  color: #9ca3af; /* 占位符颜色 */
}

/* 亮色主题下的占位符 */
.transfer-page.light-theme .message-input::placeholder {
  color: #6b7280;
}

/* 发送按钮样式 */
.send-btn {
  position: absolute;
  bottom: 12px; /* 距离输入框底部12px */
  right: 12px; /* 距离输入框右侧12px */
  background: #14a19b; /* 主题绿色 */
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0;
  font-size: 20px;
  text-align: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 56px; /* 更紧凑的尺寸 */
  height: 40px; /* 更扁平的高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(20, 161, 155, 0.3);
}

.send-btn:hover {
  background: #0f8a85; /* 悬停时稍微变暗 */
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(20, 161, 155, 0.4);
}

.send-btn:active {
  transform: translateY(0) scale(1);
  box-shadow: 0 2px 6px rgba(20, 161, 155, 0.3);
}

.send-btn:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 提示信息样式 */
.input-tips {
  font-size: 12px;
  color: #9ca3af; /* 暗色主题下提示文字颜色 */
  text-align: center;
  margin-top: 8px;
}

/* 亮色主题下的提示信息 */
.transfer-page.light-theme .input-tips {
  color: #6b7280; /* 亮色主题下提示文字颜色 */
}

/* 发送按钮图标样式 */
.send-btn .el-icon {
  font-size: 18px;
}

/* PC端专用样式 - 确保只在大屏幕上应用右下角布局 */
@media (min-width: 769px) {
  /* PC端发送按钮右下角定位 */
  .text-input-area .send-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 56px;
    height: 40px;
  }

  /* PC端输入框右侧留空间 */
  .text-input-area .message-input {
    padding-right: 80px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-sidebar {
    width: 280px;
  }
  
  .chat-header {
    padding: 12px 16px;
  }
  
  .chat-messages {
    padding: 16px;
  }
  
  .chat-input {
    padding: 16px;
  }
  
  .file-tools {
    gap: 8px;
  }
  
  .file-tool-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .input-tips {
    font-size: 11px;
  }

  /* 移动端恢复水平布局 */
  .text-input-area {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: flex-end;
  }

  .message-input {
    flex: 1;
    padding: 10px 14px; /* 移动端不需要右侧留空间 */
    min-height: 60px;
    max-height: 120px;
    font-size: 14px;
  }

  .send-btn {
    position: static; /* 重置定位 */
    width: 60px;
    height: 40px;
    flex-shrink: 0;
  }
}

@media (max-width: 480px) {
  .device-sidebar {
    width: 260px;
  }
  
  .active-device-info span {
    font-size: 14px;
  }
  
  .text-input-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .message-input {
    padding: 8px 12px; /* 小屏幕更紧凑的内边距 */
  }

  .send-btn {
    position: static; /* 确保重置定位 */
    width: 100%;
    height: 40px;
  }
}

/* 滚动条样式 */
.connected-devices::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.connected-devices::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.connected-devices::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.connected-devices::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 动画效果 */
.device-item {
  animation: fadeInLeft 0.3s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 加载状态 */
.is-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}