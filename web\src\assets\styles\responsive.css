/* 移动端响应式设计 - 根据设计图第五张精确实现 */

/* 移动端主页样式 */
@media (max-width: 768px) {
  /* 确保body和html可以滚动 */
  html, body {
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* 主页移动端适配 */
  .home-page {
    padding: 0;
    overflow-x: hidden; /* 防止水平溢出 */
    /* 移除冲突的overflow和height设置，继承主样式 */
  }

  .page-header {
    padding: 20px 16px 16px;
  }

  .logo-section {
    gap: 16px;
    margin-bottom: 20px;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }

  .logo-text {
    font-size: 32px;
  }

  /* 使用更具体的选择器确保优先级 */
  .home-page .main-content {
    padding: 0 16px 20px !important; /* 参考footer：使用16px固定边距 */
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* 移动端卡片布局顺序：扫码/配对码 -> 附近设备 -> 历史设备 */
  .home-page .pairing-section {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
    width: 100% !important; /* 确保卡片容器不超出屏幕 */
    max-width: 100% !important; /* 覆盖home-new.css中的max-width: 600px */
    margin: 0 !important; /* 移除任何外边距 */
    padding: 0 !important; /* 移除任何内边距 */
    box-sizing: border-box !important;
    /* 确保grid布局被覆盖 */
    grid-template-columns: none !important;
  }

  /* 移动端卡片顺序控制 */
  .pairing-card {
    order: 1 !important;
  }

  .nearby-devices-card {
    order: 2 !important;
  }

  .history-devices-card {
    order: 3 !important;
  }

  /* 移动端设备卡片样式 - 使用更具体的选择器，参考footer的成功实现 */
  .home-page .device-card,
  .home-page .pairing-card,
  .home-page .nearby-devices-card,
  .home-page .history-devices-card,
  .home-page .connection-card {
    height: auto !important; /* 取消固定高度 */
    min-height: auto !important;
    max-height: none !important; /* 确保没有最大高度限制 */
    width: 100% !important; /* 卡片占满容器宽度 */
    padding: 20px !important; /* 参考footer：使用固定padding */
    margin: 0 0 20px 0 !important; /* 参考footer：使用固定margin，只有底部边距 */
    box-sizing: border-box !important;
    /* 确保没有额外的边框或外边距导致溢出 */
    margin-left: 0 !important;
    margin-right: 0 !important;
    /* 重置任何可能导致溢出的属性 */
    border-left-width: 1px !important;
    border-right-width: 1px !important;
    /* 确保边框包含在宽度计算中 */
  }
  
  /* 移动端设备列表样式优化 */
  .device-list {
    overflow-y: visible; /* 移动端不需要滚动条 */
    max-height: none;
    padding-right: 0; /* 移除PC端的滚动条padding */
  }
  
  /* 隐藏移动端滚动条 */
  .device-list::-webkit-scrollbar {
    display: none;
  }
  
  /* 移动端二维码/配对卡片优化 */
  .pairing-card {
    padding: 24px 20px;
  }
  
  .qr-section {
    margin: 10px 20px;
    padding: 16px;
  }
  
  .qr-code-container {
    /* 移动端适配：设置最大宽度但保持正方形比例 */
    max-width: 200px;
    margin: 0 auto; /* 居中显示 */
  }
  
  .qr-image {
    max-width: 100%;
    height: auto; /* 保持宽高比 */
  }
  
  .pairing-code-display {
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .code-label {
    font-size: 18px;
  }
  
  .pairing-code {
    font-size: 24px;
    padding: 6px 12px;
  }
  
  .code-input-section {
    margin: 16px 20px 0 20px;
  }
  
  .current-device-section {
    margin-top: 16px;
    padding-top: 16px;
  }
  
  .connection-tabs {
    margin-bottom: 20px;
  }
  
  .connection-tabs .el-radio-group {
    width: 100%;
  }
  
  .qr-code-container {
    /* 中等屏幕：稍小的最大宽度 */
    max-width: 180px;
    margin: 0 auto;
  }
  
  .qr-image {
    max-width: 100%;
    height: auto;
  }
  
  .pairing-code-display {
    padding: 12px 16px;
    margin: 16px 0;
  }
  
  .pairing-code {
    font-size: 20px;
    letter-spacing: 2px;
  }
  
  .device-info-section {
    margin-top: 20px;
    padding-top: 16px;
    flex-direction: column;
    gap: 8px;
  }
  
  /* 移动端附近设备卡片 */
  .nearby-devices-card,
  .history-devices-card {
    min-height: auto;
  }
  
  .card-header {
    padding: 16px;
  }
  
  .device-list {
    padding: 0 16px 16px;
    overflow-y: visible; /* 移动端不使用滚动，让卡片自适应高度 */
    max-height: none; /* 移除高度限制 */
    padding-right: 16px; /* 移动端恢复正常padding */
  }
  
  /* 移动端隐藏滚动条 */
  .device-list::-webkit-scrollbar {
    display: none;
  }
  
  /* 移动端设备项 - 改为水平一行布局 */
  .device-item {
    padding: 12px !important;
    flex-direction: row !important; /* 水平布局 */
    align-items: center !important; /* 垂直居中 */
    gap: 12px !important;
    display: flex !important;
    width: 100% !important; /* 确保容器宽度 */
    box-sizing: border-box !important; /* 包含padding在宽度内 */
    overflow: hidden !important; /* 防止整体溢出 */
  }

  /* 移动端设备信息区域 */
  .device-info {
    flex: 1 !important; /* 占据剩余空间 */
    margin-bottom: 0 !important; /* 移除底部边距 */
    min-width: 0 !important; /* 允许flex项目缩小到内容以下 */
    overflow: hidden !important; /* 防止内容溢出 */
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
  }

  /* 移动端设备详情区域 */
  .device-details {
    flex: 1 !important;
    min-width: 0 !important; /* 允许缩小 */
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 2px !important;
  }

  /* 移动端设备名称 */
  .device-name {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--text-primary) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.2 !important;
  }

  /* 移动端设备元信息 */
  .device-meta {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    overflow: hidden !important;
  }

  /* 移动端最后连接时间 */
  .last-connected {
    font-size: 12px !important;
    color: var(--text-secondary) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  /* 移动端设备图标容器 */
  .device-icon-container {
    flex-shrink: 0 !important; /* 防止图标缩小 */
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 36px !important;
    height: 36px !important;
  }

  /* 移动端设备图标 */
  .device-icon {
    font-size: 20px !important;
    color: var(--primary-color) !important;
  }

  /* 移动端设备状态指示器 */
  .device-status-indicator {
    position: absolute !important;
    bottom: -2px !important;
    right: -2px !important;
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    border: 1px solid var(--background-color) !important;
  }

  .device-status-indicator.online {
    background-color: #10b981 !important; /* 绿色表示在线 */
  }

  .device-status-indicator.offline {
    background-color: #6b7280 !important; /* 灰色表示离线 */
  }

  /* 移动端设备头像（兼容旧样式） */
  .device-avatar {
    flex-shrink: 0 !important; /* 防止图标缩小 */
    width: 36px !important; /* 移动端适中的图标尺寸 */
    height: 36px !important;
  }

  /* 移动端设备操作按钮区域 */
  .device-actions {
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
    align-items: flex-end !important;
  }

  /* 移动端重连按钮 */
  .reconnect-button {
    padding: 6px 12px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    min-width: 50px !important;
    max-width: 60px !important;
    background: #14a19b !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .reconnect-button:hover:not(:disabled) {
    background: #0f8a85 !important;
    transform: translateY(-1px) !important;
  }

  .reconnect-button:active:not(:disabled) {
    background: #0d7570 !important;
    transform: translateY(0) !important;
  }

  .reconnect-button:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    background: #9ca3af !important;
  }

  /* 移动端删除按钮 */
  .remove-button {
    padding: 4px 8px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    min-width: 40px !important;
    max-width: 50px !important;
    background: #ef4444 !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  /* 移动端断开按钮（兼容旧样式） */
  .disconnect-action {
    flex-shrink: 0 !important; /* 防止按钮缩小 */
    padding: 6px 12px !important;
    font-size: 12px !important;
    white-space: nowrap !important; /* 防止文字换行 */
    min-width: 50px !important; /* 确保按钮最小宽度 */
    max-width: 60px !important; /* 限制按钮最大宽度 */
    text-align: center !important;
    background: rgba(244, 107, 107) !important; /* 👈 移动端断开按钮红色背景 */
    border-color: #f67e7e !important; /* 👈 移动端断开按钮红色边框 */
    color: #f0efef !important; /* 👈 移动端断开按钮红色文字 */
  }

  .disconnect-action:hover {
    background: rgba(239, 68, 68, 0.2) !important; /* 👈 移动端断开按钮悬停红色背景 */
    border-color: #dc2626 !important; /* 👈 移动端断开按钮悬停更深红色边框 */
    color: #dc2626 !important; /* 👈 移动端断开按钮悬停更深红色文字 */
    transform: scale(1.02) !important; /* 👈 移动端断开按钮悬停放大效果 */
  }

  /* 移动端设备名称和状态 */
  .device-name {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 2px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important; /* 防止设备名称换行 */
  }

  .device-status {
    font-size: 11px !important;
    opacity: 0.8 !important;
  }
  
  .device-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  /* 移动端底部区域 */
  .page-footer {
    background: rgba(255, 255, 255, 0.4) !important; /* 移动端40%白色背景矩形 */
    padding: 20px 16px;
    margin-top: 20px;
    border-radius: 16px; /* 添加圆角使其看起来像矩形卡片 */
    margin-left: 16px;
    margin-right: 16px;
    border: none; /* 移除边框 */
    backdrop-filter: blur(10px);
  }
  
  .dark-theme .page-footer {
    background: rgba(0, 0, 0, 0.4) !important; /* 暗色主题下40%黑色背景 */
  }
  
  .footer-content {
    flex-direction: column;
    gap: 12px;
    font-size: 12px;
    text-align: center;
  }
}

/* 移动端传输页面样式 */
@media (max-width: 768px) {
  .transfer-page.dark-theme {
    background-image: linear-gradient(to bottom, #2b343e, #2d3640, #303843, #323b45, #353d48, #353d48, #353d48, #353d48, #323b45, #303843, #2d3640, #2b343e) !important; /* 与PC端一致的渐变背景 */
  }

  .transfer-page.light-theme {
    background: linear-gradient(to top, #f3e7e9 0%, #e3eeff 99%, #e3eeff 100%) !important; /* 亮色主题背景 */
  }

  /* 移动端整体布局调整 */
  .transfer-page {
    gap: 0 !important; /* 移动端不需要间距 */
    padding: 0 !important;
  }

  /* 移动端侧边栏 */
  .device-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    width: 280px !important;
    background: rgba(0, 0, 0, 1) !important; /* 暗色主题侧边栏背景 - 100%填充 */
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
    z-index: 1000 !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .transfer-page.light-theme .device-sidebar {
    background: rgba(255, 255, 255, 1) !important; /* 亮色主题侧边栏背景 - 100%填充 */
  }

  .transfer-page.sidebar-open .device-sidebar {
    transform: translateX(0) !important; /* 侧边栏打开状态 */
  }

  /* 移动端主聊天区域 */
  .chat-main {
    flex: 1 !important;
    background: rgba(0, 0, 0, 0.4) !important; /* 与PC端一致的背景 */
    border-radius: 0 !important; /* 移动端不需要圆角 */
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100vh !important;
  }

  .transfer-page.light-theme .chat-main {
    background: rgba(255, 255, 255, 0.6) !important; /* 亮色主题背景 */
  }

  /* 移动端标题栏 */
  .chat-header {
    padding: 12px 16px !important;
    min-height: 56px !important; /* 移动端标准高度 */
    border-radius: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important; /* 移动端阴影 */
  }

  .transfer-page.light-theme .chat-header {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  /* 移动端侧边栏底部按钮布局 */
  .sidebar-footer {
    padding: 20px 16px !important; /* 增加上下内边距，给按钮更多空间 */
    gap: 12px !important;
    justify-content: space-around !important; /* 改为均匀分布，避免拉伸 */
    align-items: center !important; /* 确保垂直居中 */
    display: flex !important;
    flex-wrap: nowrap !important; /* 防止换行 */
    min-height: 60px !important; /* 确保容器有足够高度 */
    height: auto !important; /* 允许高度自适应 */
  }

  /* 移动端按钮尺寸调整 */
  .sidebar-footer .theme-btn,
  .sidebar-footer .back-btn {
    width: 36px !important;
    height: 36px !important;
    padding: 0 !important;
    border-radius: 50% !important; /* 移动端都使用圆形按钮 */
    min-width: 36px !important; /* 确保最小宽度 */
    max-width: 36px !important; /* 确保最大宽度 */
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important; /* 防止被压缩 */
    flex-grow: 0 !important; /* 防止被拉伸 */
    aspect-ratio: 1 / 1 !important; /* 强制保持1:1比例 */
  }

  /* 移动端主题切换按钮底色 */
  .sidebar-footer .theme-btn {
    background: rgba(20, 161, 155, 0.15) !important; /* 主题绿色底色 */
    border-color: rgba(20, 161, 155, 0.3) !important;
    color: #14a19b !important;
  }

  /* 移动端返回按钮底色 */
  .sidebar-footer .back-btn {
    background: rgba(59, 130, 246, 0.15) !important; /* 蓝色底色 */
    border-color: rgba(59, 130, 246, 0.3) !important;
    color: #3b82f6 !important;
  }

  /* 移动端关闭按钮 */
  .sidebar-footer .close-btn {
    width: 36px !important;
    height: 36px !important;
    padding: 0 !important;
    border-radius: 50% !important;
    min-width: 36px !important;
    max-width: 36px !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important; /* 防止被压缩 */
    flex-grow: 0 !important; /* 防止被拉伸 */
    aspect-ratio: 1 / 1 !important; /* 强制保持1:1比例 */
    background: rgba(239, 68, 68, 0.15) !important; /* 红色底色 */
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #ef4444 !important;
  }



  /* 移动端按钮悬停效果 */
  .sidebar-footer .theme-btn:hover {
    background: rgba(20, 161, 155, 0.25) !important;
    transform: scale(1.05) !important;
  }

  .sidebar-footer .back-btn:hover {
    background: rgba(59, 130, 246, 0.25) !important;
    transform: scale(1.05) !important;
  }

  .sidebar-footer .close-btn:hover {
    background: rgba(239, 68, 68, 0.25) !important;
    transform: scale(1.05) !important;
  }



  /* 移动端按钮强制圆形样式 - 覆盖所有可能的样式 */
  .sidebar-footer .el-button,
  .sidebar-footer .el-button.is-circle,
  .sidebar-footer button {
    width: 36px !important;
    height: 36px !important;
    min-height: 36px !important; /* 强制最小高度 */
    max-height: 36px !important; /* 强制最大高度 */
    padding: 0 !important;
    border-radius: 50% !important;
    min-width: 36px !important;
    max-width: 36px !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    line-height: 1 !important; /* 重置行高 */
    vertical-align: middle !important;
  }

  /* 移动端按钮额外保护 - 防止被其他样式覆盖 */
  .transfer-page .sidebar-footer .el-button {
    width: 36px !important;
    height: 36px !important;
    min-height: 36px !important;
    max-height: 36px !important;
    border-radius: 18px !important; /* 50% of 36px */
    box-sizing: border-box !important;
  }

  /* 移动端按钮图标 */
  .sidebar-footer .theme-btn .el-icon,
  .sidebar-footer .back-btn .el-icon,
  .sidebar-footer .close-btn .el-icon {
    font-size: 16px !important;
  }

  /* 移动端消息区域 */
  .chat-messages {
    flex: 1 !important;
    padding: 16px !important;
    background: transparent !important;
  }

  /* 移动端输入区域 */
  .chat-input {
    padding: 2px !important;
    background: transparent !important;
    border-radius: 0 !important;
  }

  /* 移动端文本输入区域布局 - 强制水平布局 */
  .transfer-page .text-input-area {
    display: flex !important;
    flex-direction: row !important; /* 强制水平布局 */
    gap: 12px !important;
    align-items: flex-end !important;
    width: 100% !important;
    flex-wrap: nowrap !important; /* 防止换行 */
  }

  /* 移动端文件工具按钮 */
  .file-tools {
    gap: 8px !important;
    margin-bottom: 12px !important;
  }

  .file-tool-button {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }

  /* 移动端工具栏右侧按钮区域 */
  .toolbar-actions {
    gap: 6px !important;
  }

  /* 移动端清除聊天记录按钮 */
  .btn-clear-history {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }

  /* 移动端添加设备按钮 */
  .add-device-btn.mobile {
    width: 36px !important;
    height: 36px !important;
    padding: 0 !important;
    margin-right: 8px !important;
    font-size: 16px !important;
  }

  /* 移动端输入框 */
  .message-input {
    flex: 1 !important; /* 占据剩余空间 */
    min-height: 40px !important;
    max-height: 60px !important;
    font-size: 16px !important; /* 移动端适合的字体大小 */
    width: auto !important; /* 自动宽度 */
    box-sizing: border-box !important;
  }

  /* 移动端发送按钮 - 覆盖主CSS的100%宽度设置 */
  .transfer-page .send-btn {
    flex-shrink: 0 !important; /* 防止按钮缩小 */
    width: 60px !important;
    height: 60px !important;
    font-size: 24px !important;
    min-width: 60px !important; /* 确保最小宽度 */
    max-width: 80px !important; /* 确保最大宽度 */
  }

  .send-btn .el-icon {
    font-size: 24px !important;
  }

  /* 移动端设备名称显示 */
  .active-device-info span {
    font-size: 18px !important; /* 移动端适中的字体大小 */
    font-weight: 600 !important;
  }

  /* 移动端菜单按钮 */
  .menu-btn {
    width: 36px !important;
    height: 36px !important;
  }

  /* 移动端断开按钮 */
  .header-right .el-button {
    padding: 8px 12px !important;
    font-size: 14px !important;
    background: rgba(239, 68, 68, 0.1) !important; /* 👈 移动端红色背景 */
    border-color: #ef4444 !important; /* 👈 移动端红色边框 */
    color: #ef4444 !important; /* 👈 移动端红色文字 */
  }

  .header-right .el-button:hover {
    background: rgba(239, 68, 68, 0.2) !important; /* 👈 移动端悬停红色背景 */
    border-color: #dc2626 !important; /* 👈 移动端悬停更深红色边框 */
    color: #dc2626 !important; /* 👈 移动端悬停更深红色文字 */
  }

  /* 移动端侧边栏遮罩 */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  
  .sidebar-open .mobile-sidebar {
    transform: translateX(0);
  }
  
  .sidebar-open .sidebar-overlay {
    display: block;
  }
  
  /* 移动端聊天头部 */
  .chat-header {
    height: 56px;
    padding: 0 16px;
    background: #374151 !important; /* 暗色主题头部背景 */
    border-bottom: 1px solid #334155;
  }

  .transfer-page.light-theme .chat-header {
    background: rgba(255, 255, 255, 0.95) !important; /* 亮色主题头部背景 */
    border-bottom: 1px solid #e5e7eb;
  }
  
  .menu-btn {
    display: inline-flex !important;
    background: transparent;
    border: none;
    color: var(--primary-color);
    padding: 8px;
    border-radius: 6px;
  }
  
  .active-device-info {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 500;
  }
  
  .active-device-info .el-icon {
    color: var(--primary-color);
  }
  
  /* 移动端聊天消息区域 - 移除圆角和间距 */
  .chat-messages {
    padding: 16px 0 !important; /* 上下内边距，左右无边距 */
    background: transparent !important; /* 透明背景，融入整体 */
    border-radius: 0 !important; /* 移除圆角 */
    margin: 0 !important; /* 移除外边距 */
    flex: 1 !important; /* 占据剩余空间 */
  }

  .transfer-page.light-theme .chat-messages {
    background: transparent !important; /* 亮色主题也使用透明背景 */
  }

  .welcome-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
  }

  .welcome-screen .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  /* 移动端输入区域 - 移除背景和边框 */
  .chat-input {
    padding: 0 8px 10px 8px !important; /* 移除顶部内边距 */
    background: transparent !important; /* 透明背景 */
    border-top: none !important; /* 移除顶部边框 */
    gap: 1px !important; /* 工具栏和输入区域之间1px间距 */
  }

  .transfer-page.light-theme .chat-input {
    background: transparent !important; /* 亮色主题也使用透明背景 */
    border-top: none !important; /* 移除顶部边框 */
  }
  
  .input-toolbar {
    justify-content: flex-start !important; /* 左对齐 */
    margin-bottom: 1px !important; /* 与输入框1px间距 */
    gap: 4px !important; /* 按钮之间间距改为4px */
    padding: 0 16px !important; /* 与输入框左右对齐 */
  }
  
  .file-tool-button {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .text-input-area {
    gap: 6px !important; /* 输入框和发送按钮之间的间距 */
    align-items: flex-end !important; /* 确保底部对齐 */
    padding: 0 16px !important; /* 与工具栏左右对齐 */
    margin: 0 !important; /* 移除外边距 */
  }
  
  .message-input {
    min-height: 44px !important; /* 增加输入框高度，更适合触摸 */
    max-height: 120px !important; /* 限制最大高度 */
    padding: 12px 16px !important; /* 增加内边距 */
    font-size: 16px !important; /* 防止iOS缩放 */
    line-height: 1.4 !important;
    background: #4b5563 !important; /* 暗色主题输入框背景 */
    border: 1px solid #6b7280 !important;
    border-radius: 12px !important; /* 增加圆角 */
    color: #f9fafb !important;
    resize: none !important; /* 禁用手动调整大小 */
    word-wrap: break-word !important; /* 允许长单词换行 */
    word-break: break-word !important; /* 强制长单词换行 */
    white-space: pre-wrap !important; /* 保持空格和换行，允许自动换行 */
    overflow-wrap: break-word !important; /* 现代浏览器的换行属性 */
    box-sizing: border-box !important; /* 确保padding计算正确 */
  }

  .transfer-page.light-theme .message-input {
    background: #ffffff !important; /* 亮色主题输入框背景 */
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
  }
  
  .send-btn {
    width: 56px !important; /* 稍微增加宽度 */
    height: 70px !important; /* 与输入框总高度一致 (44+24+2) */
    min-width: 56px !important;
    min-height: 70px !important;
    font-size: 18px !important;
    border-radius: 12px !important; /* 与输入框圆角一致 */
    flex-shrink: 0 !important; /* 防止按钮缩小 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important; /* 移除内边距，让图标居中 */
  }
  
  .input-tips {
    font-size: 11px;
    margin-top: 6px;
  }
  
  /* 移动端侧边栏底部按钮 */
  .sidebar-footer {
    padding: 12px;
    display: flex;
    gap: 8px;
    justify-content: space-between;
  }
  
  .sidebar-footer .el-button {
    height: 32px;
    font-size: 12px;
    flex: 1;
  }
  
  /* 移动端侧边栏增加关闭按钮 */
  .sidebar-close-btn {
    background: rgba(239, 68, 68, 0.1) !important; /* 👈 移动端红色背景 */
    border: 1px solid #ef4444 !important; /* 👈 移动端红色边框 */
    color: #ef4444 !important; /* 👈 移动端红色文字 */
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease; /* 👈 完整的过渡效果 */
  }

  .sidebar-close-btn:hover {
    background: rgba(239, 68, 68, 0.2) !important; /* 👈 移动端悬停红色背景 */
    border-color: #dc2626 !important; /* 👈 移动端悬停更深红色边框 */
    color: #dc2626 !important; /* 👈 移动端悬停更深红色文字 */
    transform: scale(1.02) !important; /* 👈 移动端悬停放大效果 */
  }
}

/* iPad纵向屏幕适配 */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  /* iPad纵向屏幕主页适配 */
  .page-header {
    padding: 24px 20px 20px;
  }

  .logo-section {
    gap: 18px;
    margin-bottom: 24px;
  }

  .logo-icon {
    width: 72px;
    height: 72px;
  }

  .logo-text {
    font-size: 36px;
  }

  /* iPad纵向屏幕主内容区域 */
  .home-page .main-content {
    padding: 0 24px 24px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* iPad纵向屏幕卡片布局 */
  .home-page .pairing-section {
    display: flex !important;
    flex-direction: column !important;
    gap: 24px !important;
    width: 100% !important;
    max-width: 600px !important; /* iPad纵向适合的最大宽度 */
    margin: 0 auto !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }

  /* iPad纵向屏幕设备卡片样式 */
  .home-page .device-card,
  .home-page .pairing-card,
  .home-page .nearby-devices-card,
  .home-page .history-devices-card,
  .home-page .connection-card {
    height: auto !important;
    min-height: auto !important;
    width: 100% !important;
    padding: 24px !important;
    margin: 0 0 24px 0 !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* iPad纵向屏幕二维码优化 */
  .qr-section {
    margin: 40px 120px !important; /* 增加左右边距，减少宽度约五分之一 */
    padding: 20px 20px 60px 20px !important; /* 增加底部内边距40px，总高度增加40px */
    border-radius: 20px !important; /* 保持较大的圆角 */
  }

  .qr-code-container {
    max-width: 240px; /* iPad纵向适合的二维码尺寸 */
    margin: 0 auto;
  }

  .qr-image {
    max-width: 100%;
    height: auto;
  }

  .pairing-code-display {
    gap: 12px;
    margin-bottom: 20px;
  }

  .code-label {
    font-size: 22px;
  }

  .pairing-code {
    font-size: 28px;
    padding: 8px 16px;
  }

  /* iPad纵向屏幕输入框和按钮适配 */
  .code-input-section {
    margin: 20px 120px 0 120px !important; /* 与qr-section保持一致的左右边距 */
  }

  .code-input {
    width: 100% !important;
    padding: 12px 16px !important;
    font-size: 18px !important;
    border-radius: 10px !important;
    margin-bottom: 16px !important;
    box-sizing: border-box !important;
  }

  .connect-button-large {
    width: 100% !important;
    padding: 14px 16px !important;
    font-size: 18px !important;
    border-radius: 10px !important;
    box-sizing: border-box !important;
  }

  /* iPad纵向屏幕设备列表优化 */
  .device-list {
    overflow-y: visible;
    max-height: none;
    padding-right: 0;
  }

  .card-title {
    font-size: 20px;
  }

  /* iPad纵向屏幕底部区域 */
  .page-footer {
    background: rgba(255, 255, 255, 0.4) !important;
    padding: 24px 20px;
    margin: 24px 24px 20px 24px;
    border-radius: 16px;
    border: none;
    backdrop-filter: blur(10px);
  }

  .dark-theme .page-footer {
    background: rgba(0, 0, 0, 0.4) !important;
  }

  .footer-content {
    flex-direction: row; /* iPad纵向可以保持横向布局 */
    gap: 20px;
    font-size: 14px;
    text-align: center;
  }

  /* iPad纵向屏幕传输页面适配 */
  .transfer-page {
    background: #374151;
  }

  /* iPad纵向屏幕聊天头部 */
  .chat-header {
    height: 64px;
    padding: 0 24px;
    background: #0f1c2e;
    border-bottom: 1px solid #334155;
  }

  .active-device-info {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 500;
  }

  /* iPad纵向屏幕聊天消息区域 - 一体化布局 */
  .chat-messages {
    padding: 20px 0 !important; /* 上下内边距，左右无边距 */
    background: transparent !important; /* 透明背景，融入整体 */
    border-radius: 0 !important; /* 移除圆角 */
    margin: 0 !important; /* 移除外边距 */
    flex: 1 !important; /* 占据剩余空间 */
  }

  .transfer-page.light-theme .chat-messages {
    background: transparent !important; /* 亮色主题也使用透明背景 */
  }

  /* iPad纵向屏幕输入区域 - 一体化布局 */
  .chat-input {
    padding: 0 24px 20px 24px !important; /* 移除顶部内边距 */
    background: transparent !important; /* 透明背景 */
    border-top: none !important; /* 移除顶部边框 */
    gap: 1px !important; /* 工具栏和输入区域之间1px间距 */
  }

  .transfer-page.light-theme .chat-input {
    background: transparent !important; /* 亮色主题也使用透明背景 */
    border-top: none !important; /* 移除顶部边框 */
  }

  .input-toolbar {
    justify-content: flex-start !important; /* 左对齐 */
    margin-bottom: 1px !important; /* 与输入框1px间距 */
    gap: 4px !important; /* iPad按钮间距改为4px */
    padding: 0 24px !important; /* 与输入框左右对齐 */
  }

  .file-tool-button {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .text-input-area {
    display: flex !important; /* 确保flex布局 */
    flex-direction: row !important; /* 水平排列 */
    gap: 12px !important; /* iPad输入框和发送按钮间距 */
    align-items: flex-end !important;
    padding: 0 24px !important; /* 与工具栏左右对齐 */
    margin: 0 !important;
  }

  .message-input {
    flex: 1 !important; /* 占据剩余空间 */
    font-size: 16px;
    padding: 12px 16px;
    background: #4b5563 !important; /* 暗色主题输入框背景 */
    border: 1px solid #6b7280 !important;
    color: #f9fafb !important;
    min-height: 48px !important;
    max-height: 120px !important;
  }

  .send-btn {
    position: static !important; /* 重置PC端的绝对定位 */
    width: 60px !important; /* 稍微增加宽度 */
    height: 72px !important; /* 与iPad输入框总高度一致 (48+24+2) */
    flex-shrink: 0 !important; /* 防止按钮缩小 */
  }

  .transfer-page.light-theme .message-input {
    background: #ffffff !important; /* 亮色主题输入框背景 */
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
  }
}

/* 平板设备纵向屏幕适配 - 更宽泛的条件 */
@media (min-width: 768px) and (max-width: 1024px) {
  .qr-section {
    margin: 40px 120px !important; /* 确保在所有平板设备上生效 */
    padding: 20px 20px 60px 20px !important;
    border-radius: 20px !important;
  }

  /* 输入框和按钮也需要适配 */
  .code-input-section {
    margin: 20px 120px 0 120px !important;
  }

  .code-input {
    width: 100% !important;
    padding: 12px 16px !important;
    font-size: 18px !important;
    border-radius: 10px !important;
    margin-bottom: 16px !important;
    box-sizing: border-box !important;
  }

  .connect-button-large {
    width: 100% !important;
    padding: 14px 16px !important;
    font-size: 18px !important;
    border-radius: 10px !important;
    box-sizing: border-box !important;
  }

  /* 平板设备传输页面一体化布局 */
  .transfer-page .chat-messages {
    padding: 20px 0 !important;
    background: transparent !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }

  .transfer-page .chat-input {
    padding: 0 20px 20px 20px !important;
    background: transparent !important;
    border-top: none !important;
    gap: 1px !important;
  }

  .transfer-page .input-toolbar {
    justify-content: flex-start !important;
    margin-bottom: 1px !important;
    gap: 4px !important; /* 平板设备按钮间距改为4px */
    padding: 0 20px !important;
  }

  .transfer-page .text-input-area {
    display: flex !important;
    flex-direction: row !important;
    padding: 0 20px !important;
    gap: 12px !important;
    align-items: flex-end !important;
  }

  .transfer-page .message-input {
    flex: 1 !important;
  }

  .transfer-page .send-btn {
    position: static !important;
    width: 60px !important;
    height: 72px !important; /* 与平板输入框高度一致 */
    flex-shrink: 0 !important;
  }
}

/* iPad横向屏幕适配 */
@media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  /* iPad横向屏幕可以使用桌面布局，但需要一些调整 */
  .home-page .main-content {
    padding: 0 32px 32px !important;
  }

  .home-page .pairing-section {
    display: grid !important;
    grid-template-columns: 1fr 1fr 1fr !important;
    gap: 28px !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
  }

  .home-page .device-card {
    height: 520px !important; /* 稍微降低高度适应横向屏幕 */
    padding: 20px !important;
  }

  /* iPad横向屏幕二维码优化 */
  .qr-section {
    margin: 8px 24px;
    padding: 16px;
  }

  .qr-code-container {
    max-width: 180px;
    margin: 0 auto;
  }

  .pairing-code {
    font-size: 24px;
  }

  .code-label {
    font-size: 18px;
  }

  /* iPad横向屏幕传输页面一体化布局 */
  .transfer-page .chat-messages {
    padding: 20px 0 !important;
    background: transparent !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }

  .transfer-page .chat-input {
    padding: 0 24px 20px 24px !important;
    background: transparent !important;
    border-top: none !important;
    gap: 1px !important;
  }

  .transfer-page .input-toolbar {
    justify-content: flex-start !important;
    margin-bottom: 1px !important;
    gap: 4px !important; /* iPad横向按钮间距改为4px */
    padding: 0 24px !important;
  }

  .transfer-page .text-input-area {
    display: flex !important;
    flex-direction: row !important;
    padding: 0 24px !important;
    gap: 12px !important;
    align-items: flex-end !important;
  }

  .transfer-page .message-input {
    flex: 1 !important;
  }

  .transfer-page .send-btn {
    position: static !important;
    width: 60px !important;
    height: 72px !important; /* 与iPad横向输入框高度一致 */
    flex-shrink: 0 !important;
  }
}

/* 小屏移动端适配 */
@media (max-width: 480px) {
  /* 主页小屏适配 */
  .page-header {
    padding: 16px 12px 12px;
  }
  
  .logo-section {
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .logo-icon {
    width: 48px;
    height: 48px;
  }
  
  .logo-text {
    font-size: 24px;
  }
  
  /* 使用更具体的选择器确保优先级 */
  .home-page .main-content {
    padding: 0 12px 16px !important; /* 参考小屏footer：使用12px固定边距 */
    width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .home-page .pairing-section {
    gap: 16px !important;
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 小屏设备卡片样式 - 使用更具体的选择器 */
  .home-page .device-card,
  .home-page .pairing-card,
  .home-page .nearby-devices-card,
  .home-page .history-devices-card,
  .home-page .connection-card {
    padding: 16px !important; /* 参考小屏footer：使用16px固定padding */
    width: 100% !important;
    margin: 0 0 16px 0 !important; /* 参考footer间距：使用固定margin */
    box-sizing: border-box !important;
    /* 确保小屏幕下也不会溢出 */
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  /* 小屏设备列表样式优化 */
  .device-list {
    padding-right: 0; /* 移除PC端的滚动条padding */
  }
  
  .card-title {
    font-size: 18px;
  }
  
  /* 配对卡片小屏优化 */
  .pairing-card {
    padding: 20px 16px;
  }
  
  .qr-section {
    margin: 8px 16px;
    padding: 12px;
  }
  
  .qr-code-container {
    /* 小屏幕：更小的最大宽度 */
    max-width: 160px;
    margin: 0 auto;
  }
  
  .qr-image {
    max-width: 100%;
    height: auto;
  }
  
  .code-label {
    font-size: 16px;
  }
  
  .pairing-code {
    font-size: 20px;
    padding: 4px 8px;
  }
  
  .code-input-section {
    margin: 12px 16px 0 16px;
  }
  
  /* 设备列表小屏优化 */
  .card-header {
    padding: 12px;
  }
  
  .device-list {
    padding: 0 12px 12px;
  }
  
  /* 小屏幕设备项 - 保持水平布局 */
  .device-item {
    padding: 10px !important;
    flex-direction: row !important; /* 确保水平布局 */
    align-items: center !important;
    gap: 8px !important;
  }

  /* 小屏幕设备信息区域 */
  .device-info {
    flex: 1 !important;
    min-width: 0 !important;
    gap: 8px !important;
  }

  /* 小屏幕设备详情 */
  .device-details {
    flex: 1 !important;
    min-width: 0 !important;
    gap: 1px !important;
  }

  /* 小屏幕设备名称 */
  .device-name {
    font-size: 13px !important;
    line-height: 1.1 !important;
  }

  /* 小屏幕设备元信息 */
  .last-connected {
    font-size: 11px !important;
  }

  /* 小屏幕设备图标容器 */
  .device-icon-container {
    width: 32px !important;
    height: 32px !important;
  }

  .device-icon {
    font-size: 18px !important;
  }

  .device-status-indicator {
    width: 6px !important;
    height: 6px !important;
  }

  .device-actions {
    flex-direction: column;
    gap: 6px;
    width: auto;
    flex-shrink: 0 !important;
  }

  /* 小屏幕重连按钮 */
  .reconnect-button {
    padding: 5px 10px !important;
    font-size: 11px !important;
    min-width: 45px !important;
    max-width: 55px !important;
    background: #14a19b !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
  }

  /* 小屏幕删除按钮 */
  .remove-button {
    padding: 3px 6px !important;
    font-size: 10px !important;
    min-width: 35px !important;
    max-width: 45px !important;
  }

  .reconnect-button,
  .remove-button {
    width: 100%;
    margin-left: 0;
  }
  
  /* 小屏Footer优化 */
  .page-footer {
    margin: 16px 12px 12px 12px !important;
    padding: 16px 12px !important;
  }
  
  .footer-content {
    gap: 10px;
    font-size: 11px;
  }
  
  /* 传输页面小屏适配 */
  .chat-header {
    height: 52px;
    padding: 0 12px;
  }
  
  .active-device-info {
    font-size: 14px;
  }
  
  .chat-messages {
    padding: 12px 0 !important; /* 小屏幕消息区域无左右边距 */
    background: transparent !important;
    margin: 0 !important;
  }

  .chat-input {
    padding: 0 12px 16px 12px !important; /* 小屏幕紧凑间距 */
    background: transparent !important;
    border-top: none !important;
    gap: 1px !important;
  }
  
  .file-tool-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .input-toolbar {
    gap: 4px !important; /* 小屏幕按钮间距改为4px */
    margin-bottom: 1px !important; /* 与输入框1px间距 */
    padding: 0 12px !important; /* 与输入框左右对齐 */
    justify-content: flex-start !important; /* 左对齐 */
  }
  
  .text-input-area {
    padding: 0 12px !important; /* 与工具栏对齐 */
    gap: 10px !important;
  }

  .message-input {
    min-height: 40px !important; /* 小屏幕保持合适的触摸高度 */
    max-height: 100px !important;
    padding: 10px 14px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 10px !important;
    line-height: 1.4 !important;
  }
  
  .send-btn {
    width: 48px !important; /* 稍微增加宽度 */
    height: 62px !important; /* 与小屏幕输入框总高度一致 (40+20+2) */
    min-width: 48px !important;
    min-height: 62px !important;
    font-size: 16px !important;
    border-radius: 10px !important;
    flex-shrink: 0 !important;
  }
  
  .input-tips {
    font-size: 10px;
    margin-top: 4px;
  }
}

/* 移动端专用动画 */
@media (max-width: 768px) {
  .sidebar-enter-active,
  .sidebar-leave-active {
    transition: transform 0.3s ease;
  }
  
  .sidebar-enter-from {
    transform: translateX(-100%);
  }
  
  .sidebar-leave-to {
    transform: translateX(-100%);
  }
  
  /* 移动端触摸优化 */
  .device-item,
  .file-tool-button,
  .connect-button,
  .disconnect-button,
  .refresh-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
  
  /* 移动端滚动优化 */
  .device-list,
  .chat-messages {
    -webkit-overflow-scrolling: touch;
  }
}

/* 横屏适配 */
@media (max-width: 768px) and (orientation: landscape) {
  .page-header {
    padding: 12px 16px 8px;
  }
  
  .logo-section {
    margin-bottom: 12px;
  }
  
  .main-content {
    padding: 0 16px 12px;
  }
  
  .pairing-section {
    gap: 16px;
  }
  
  .connection-card {
    padding: 16px;
  }
  
  .qr-code-container {
    width: 120px;
    height: 120px;
  }
  
  .device-list {
    max-height: 120px;
  }
  
  /* 传输页面横屏适配 */
  .chat-header {
    height: 48px;
  }
  
  .chat-input {
    padding: 8px 16px;
  }
  
  .input-toolbar {
    margin-bottom: 8px;
  }
  
  .file-tool-button {
    width: 36px;
    height: 36px;
  }
}

/* 超小屏设备适配 */
@media (max-width: 360px) {
  .page-header {
    padding: 12px 8px 8px;
  }
  
  .main-content {
    padding: 0 8px 12px;
  }
  
  .connection-card {
    padding: 16px 12px;
  }
  
  .qr-code-container {
    width: 120px;
    height: 120px;
  }
  
  .pairing-code {
    font-size: 18px;
    letter-spacing: 1px;
  }
  
  .card-header {
    padding: 10px;
  }
  
  .device-list {
    padding: 0 10px 10px;
  }
  
  /* 传输页面超小屏适配 */
  .chat-header {
    padding: 0 8px;
  }
  
  .chat-messages {
    padding: 8px 0 !important; /* 超小屏幕无左右边距 */
    background: transparent !important;
    margin: 0 !important;
  }

  .chat-input {
    padding: 0 8px 14px 8px !important; /* 超小屏幕紧凑间距 */
    background: transparent !important;
    border-top: none !important;
    gap: 1px !important;
  }
  
  .file-tool-button {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .input-toolbar {
    gap: 4px !important; /* 超小屏幕最小间距 */
    margin-bottom: 1px !important; /* 与输入框1px间距 */
    padding: 0 8px !important; /* 与输入框对齐 */
    justify-content: flex-start !important;
  }

  .text-input-area {
    padding: 0 8px !important; /* 与工具栏对齐 */
    gap: 8px !important;
  }
}

/* 高分辨率移动设备适配 */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .logo-icon,
  .qr-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 深色模式移动端优化 */
@media (max-width: 768px) {
  /* 移除 page-header 的背景色，让Logo区域透明 */
  
  .dark-theme .chat-header {
    background: #0f1c2e;
  }
  
  .dark-theme .menu-btn {
    color: var(--primary-color);
  }
  
  .dark-theme .active-device-info {
    color: var(--primary-color);
  }
}

/* 亮色模式移动端优化 */
@media (max-width: 768px) {
  /* 移除 page-header 的背景色和阴影，让Logo区域透明 */
  
  .light-theme .chat-header {
    background: white;
    border-bottom-color: #e5e7eb;
  }
  
  .light-theme .chat-messages {
    background: #f8f9fa;
  }
  
  .light-theme .chat-input {
    background: white;
  }
  
  .light-theme .menu-btn {
    color: var(--primary-color);
  }
  
  .light-theme .active-device-info {
    color: #1f2937;
  }
} 