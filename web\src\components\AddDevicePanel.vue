<template>
  <div class="add-device-panel" :class="{ 'dark-theme': isDarkMode }">
    <!-- 面板标题 -->
    <div class="panel-header">
      <h3 class="panel-title">添加新设备</h3>
      <button class="close-btn" @click="$emit('close')" title="关闭">
        <el-icon><Close /></el-icon>
      </button>
    </div>

    <!-- 二维码/配对码区域 -->
    <div class="qr-section">
      <!-- 二维码 -->
      <div class="qr-code-container">
        <div class="qr-code" ref="qrcodeElement"></div>
      </div>
      
      <!-- 配对码显示 -->
      <div class="pairing-code-display">
        <span class="code-label">本机</span>
        <span class="pairing-code">{{ pairingCode || '0000' }}</span>
        <button 
          class="refresh-code-btn"
          @click="$emit('refresh-code')"
          :disabled="refreshing"
          title="刷新配对码"
        >
          <el-icon :class="{ 'is-loading': refreshing }"><Refresh /></el-icon>
        </button>
      </div>
    </div>

    <!-- 配对码输入区域 -->
    <div class="code-input-section">
      <div class="input-group">
        <input 
          v-model="inputCode"
          placeholder="输入配对码"
          maxlength="4"
          class="code-input"
          @keyup.enter="handleConnect"
        />
        <button 
          class="connect-btn"
          @click="handleConnect"
          :disabled="inputCode.length !== 4 || connecting"
          title="连接"
        >
          <el-icon :class="{ 'is-loading': connecting }"><Connection /></el-icon>
        </button>
      </div>
    </div>

    <!-- 附近设备区域 -->
    <div class="nearby-devices-section">
      <div class="section-header">
        <h4 class="section-title">附近的设备</h4>
        <button 
          class="refresh-devices-btn"
          @click="$emit('discover-devices')"
          :disabled="discovering"
          title="刷新设备"
        >
          <el-icon :class="{ 'is-loading': discovering }"><Search /></el-icon>
        </button>
      </div>
      
      <div class="devices-list" :class="{ 'has-devices': availableDevices.length > 0 }">
        <div 
          v-for="device in availableDevices" 
          :key="device.id"
          class="device-item"
          @click="handleDeviceConnect(device)"
        >
          <div class="device-info">
            <el-icon size="16" class="device-icon">
              <Monitor v-if="device.type === 'desktop'" />
              <Iphone v-else />
            </el-icon>
            <div class="device-details">
              <div class="device-name">{{ getDeviceDisplayName(device) }}</div>
              <div class="device-ip">{{ device.ip }}</div>
            </div>
          </div>
          <button 
            class="device-connect-btn"
            :disabled="connectingDevices.has(device.id)"
          >
            {{ connectingDevices.has(device.id) ? '连接中' : '连接' }}
          </button>
        </div>
        
        <!-- 空状态 -->
        <div v-if="availableDevices.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Search /></el-icon>
          <p class="empty-text">未发现设备</p>
          <p class="empty-tip">请确保设备在同一网络下</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Close, Refresh, Connection, Search, Monitor, Iphone 
} from '@element-plus/icons-vue'
import QRCode from 'qrcode'

// Props
const props = defineProps({
  pairingCode: {
    type: String,
    default: ''
  },
  availableDevices: {
    type: Array,
    default: () => []
  },
  connectingDevices: {
    type: Set,
    default: () => new Set()
  },
  currentDevice: {
    type: Object,
    default: () => ({})
  },
  refreshing: {
    type: Boolean,
    default: false
  },
  discovering: {
    type: Boolean,
    default: false
  },
  isDarkMode: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'close',
  'refresh-code', 
  'discover-devices',
  'connect-by-code',
  'connect-to-device'
])

// 响应式数据
const qrcodeElement = ref(null)
const inputCode = ref('')
const connecting = ref(false)

// 计算属性
const currentOrigin = computed(() => window.location.origin)

// 生成二维码
const generateQRCode = async () => {
  if (!qrcodeElement.value || !props.pairingCode) {
    return
  }

  try {
    qrcodeElement.value.innerHTML = ''

    const connectionInfo = {
      type: 'wsshare_connection',
      code: props.pairingCode,
      device: props.currentDevice.name,
      ip: props.currentDevice.ip,
      url: currentOrigin.value,
      version: '1.0'
    }

    // 根据屏幕尺寸调整二维码大小
    const getQRSize = () => {
      const width = window.innerWidth
      if (width <= 480) return 100  // 手机
      if (width <= 768) return 120  // 小屏平板
      if (width <= 1024) return 140 // 平板
      return 120 // 桌面
    }

    const qrDataURL = await QRCode.toDataURL(JSON.stringify(connectionInfo), {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: props.isDarkMode ? '#ffffff' : '#000000',
        light: props.isDarkMode ? '#1a1a1a' : '#ffffff'
      },
      width: getQRSize()
    })

    qrcodeElement.value.innerHTML = `<img src="${qrDataURL}" alt="连接二维码" class="qr-image" />`
  } catch (error) {
    qrcodeElement.value.innerHTML = `
      <div class="qr-error">
        <el-icon><Close /></el-icon>
        <span>二维码生成失败</span>
      </div>
    `
  }
}

// 处理配对码连接
const handleConnect = () => {
  if (inputCode.value.length !== 4) {
    ElMessage.warning('请输入4位配对码')
    return
  }
  
  connecting.value = true
  emit('connect-by-code', inputCode.value)
  
  // 重置连接状态
  setTimeout(() => {
    connecting.value = false
    inputCode.value = ''
  }, 3000)
}

// 处理设备连接
const handleDeviceConnect = (device) => {
  emit('connect-to-device', device)
}

// 获取设备显示名称
const getDeviceDisplayName = (device) => {
  if (!device) return '未知设备'
  return device.model_name || device.deviceName || device.name || '未知设备'
}

// 监听配对码变化，重新生成二维码
watch(() => props.pairingCode, () => {
  nextTick(() => {
    generateQRCode()
  })
}, { immediate: true })

// 监听主题变化，重新生成二维码
watch(() => props.isDarkMode, () => {
  nextTick(() => {
    generateQRCode()
  })
})
</script>

<style scoped>
.add-device-panel {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  width: 280px;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  /* 确保在小屏设备上不会被遮挡 */
  max-width: calc(100vw - 32px);
  max-height: calc(100vh - 120px);
}

.add-device-panel.dark-theme {
  background: rgba(30, 30, 30, 0.95);
  border-color: #404040;
  color: #ffffff;
}

/* 面板标题 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.dark-theme .panel-header {
  border-bottom-color: #404040;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1a1a1a;
}

.dark-theme .panel-title {
  color: #ffffff;
}

.close-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.dark-theme .close-btn {
  color: #999;
}

.dark-theme .close-btn:hover {
  background: #404040;
  color: #fff;
}

/* 二维码/配对码区域 */
.qr-section {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  text-align: center;
}

.dark-theme .qr-section {
  background: rgba(40, 40, 40, 0.8);
  border-color: #505050;
}

.qr-code-container {
  margin-bottom: 8px;
}

.qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.qr-image {
  border-radius: 4px;
}

.qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 12px;
}

.pairing-code-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
}

.code-label {
  color: #666;
  font-size: 12px;
}

.dark-theme .code-label {
  color: #999;
}

.pairing-code {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: bold;
  color: #14a19b;
  letter-spacing: 2px;
}

.dark-theme .pairing-code {
  color: #4ade80;
}

.refresh-code-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.refresh-code-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #333;
}

.dark-theme .refresh-code-btn:hover:not(:disabled) {
  background: #505050;
  color: #fff;
}

/* 配对码输入区域 */
.code-input-section {
  margin-bottom: 12px;
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.code-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  transition: all 0.2s ease;
}

.code-input:focus {
  outline: none;
  border-color: #14a19b;
  box-shadow: 0 0 0 2px rgba(20, 161, 155, 0.1);
}

.dark-theme .code-input {
  background: #2a2a2a;
  border-color: #505050;
  color: #ffffff;
}

.dark-theme .code-input:focus {
  border-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.1);
}

.connect-btn {
  padding: 8px;
  background: #14a19b;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connect-btn:hover:not(:disabled) {
  background: #0f8a84;
}

.connect-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

.dark-theme .connect-btn {
  background: #4ade80;
}

.dark-theme .connect-btn:hover:not(:disabled) {
  background: #22c55e;
}

/* 附近设备区域 */
.nearby-devices-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  color: #374151;
}

.dark-theme .section-title {
  color: #d1d5db;
}

.refresh-devices-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.refresh-devices-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #333;
}

.dark-theme .refresh-devices-btn:hover:not(:disabled) {
  background: #404040;
  color: #fff;
}

.devices-list {
  flex: 1;
  overflow-y: auto;
  min-height: 120px;
  max-height: 200px;
  /* 确保滚动条在需要时显示 */
  overflow-x: hidden;
}

.devices-list.has-devices {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px;
}

.dark-theme .devices-list.has-devices {
  border-color: #404040;
}

.device-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}

.device-item:hover {
  background: #f8fafc;
}

.dark-theme .device-item:hover {
  background: #374151;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.device-icon {
  color: #6b7280;
  flex-shrink: 0;
}

.dark-theme .device-icon {
  color: #9ca3af;
}

.device-details {
  flex: 1;
  min-width: 0;
}

.device-name {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark-theme .device-name {
  color: #d1d5db;
}

.device-ip {
  font-size: 10px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark-theme .device-ip {
  color: #9ca3af;
}

.device-connect-btn {
  background: #14a19b;
  border: none;
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.device-connect-btn:hover:not(:disabled) {
  background: #0f8a84;
}

.device-connect-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

.dark-theme .device-connect-btn {
  background: #4ade80;
}

.dark-theme .device-connect-btn:hover:not(:disabled) {
  background: #22c55e;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #6b7280;
}

.dark-theme .empty-state {
  color: #9ca3af;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-text {
  font-size: 12px;
  margin: 0 0 4px;
}

.empty-tip {
  font-size: 10px;
  margin: 0;
  opacity: 0.7;
}

/* 滚动条样式 */
.devices-list::-webkit-scrollbar {
  width: 4px;
}

.devices-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.devices-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.devices-list::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.dark-theme .devices-list::-webkit-scrollbar-track {
  background: #404040;
}

.dark-theme .devices-list::-webkit-scrollbar-thumb {
  background: #606060;
}

.dark-theme .devices-list::-webkit-scrollbar-thumb:hover {
  background: #707070;
}

/* 滚动条样式 */
.devices-list::-webkit-scrollbar {
  width: 4px;
}

.devices-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.devices-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
  transition: background 0.2s ease;
}

.devices-list::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.dark-theme .devices-list::-webkit-scrollbar-track {
  background: #404040;
}

.dark-theme .devices-list::-webkit-scrollbar-thumb {
  background: #606060;
}

.dark-theme .devices-list::-webkit-scrollbar-thumb:hover {
  background: #707070;
}

/* 加载动画 */
.is-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 - 平板设备 */
@media (max-width: 1024px) and (min-width: 769px) {
  .add-device-panel {
    width: 300px;
    max-height: calc(100vh - 100px);
    padding: 18px;
  }

  .qr-section {
    padding: 14px;
  }

  .qr-code {
    min-height: 140px;
  }

  .devices-list {
    min-height: 140px;
    max-height: 220px;
  }
}

/* 响应式设计 - 小屏平板/大手机 */
@media (max-width: 768px) and (min-width: 481px) {
  .add-device-panel {
    width: 320px;
    max-width: calc(100vw - 24px);
    max-height: calc(100vh - 80px);
    padding: 16px;
  }

  .panel-title {
    font-size: 15px;
  }

  .qr-section {
    padding: 12px;
    margin-bottom: 10px;
  }

  .qr-code {
    min-height: 120px;
  }

  .code-input-section {
    margin-bottom: 10px;
  }

  .devices-list {
    min-height: 120px;
    max-height: 180px;
  }

  .device-item {
    padding: 10px 8px;
  }

  .device-name {
    font-size: 13px;
  }

  .device-ip {
    font-size: 11px;
  }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 480px) {
  .add-device-panel {
    width: 100%;
    max-width: calc(100vw - 16px);
    max-height: calc(100vh - 60px);
    padding: 14px;
    border-radius: 8px;
  }

  .panel-header {
    margin-bottom: 12px;
    padding-bottom: 6px;
  }

  .panel-title {
    font-size: 14px;
  }

  .qr-section {
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
  }

  .qr-code {
    min-height: 100px;
  }

  .qr-image {
    width: 100px !important;
    height: 100px !important;
  }

  .pairing-code {
    font-size: 16px;
    letter-spacing: 1px;
  }

  .code-input-section {
    margin-bottom: 8px;
  }

  .code-input {
    padding: 6px 10px;
    font-size: 13px;
  }

  .connect-btn {
    padding: 6px;
  }

  .section-title {
    font-size: 13px;
  }

  .devices-list {
    min-height: 100px;
    max-height: 150px;
  }

  .device-item {
    padding: 8px 6px;
    margin-bottom: 1px;
  }

  .device-name {
    font-size: 12px;
  }

  .device-ip {
    font-size: 10px;
  }

  .device-connect-btn {
    padding: 3px 6px;
    font-size: 9px;
  }

  .empty-state {
    padding: 15px;
  }

  .empty-icon {
    font-size: 20px;
  }

  .empty-text {
    font-size: 11px;
  }

  .empty-tip {
    font-size: 9px;
  }
}
</style>
