<template>
  <!-- 使用遮罩层 + 卡片的方式，而不是el-dialog -->
  <div v-if="visible" class="pairing-dialog-overlay" @click="handleOverlayClick">
    <!-- 主卡片 - 使用与主页相同的device-card类 -->
    <div class="device-card pairing-dialog-card" :class="{ 'dark-theme': appStore.isDarkMode }" @click.stop>
      <!-- 卡片头部 -->
      <div class="card-header">
        <h3 class="card-title">添加新设备</h3>
        <el-button
          @click="handleClose"
          :icon="Close"
          circle
          size="small"
          class="close-btn"
        />
      </div>

      <!-- 卡片内容 -->
      <div class="card-content">
        <!-- 二维码/配对码区域 - 参考主页的qr-section -->
        <div class="qr-section">
          <!-- 二维码 -->
          <div class="qr-code-container">
            <div ref="qrCodeRef" class="qr-code"></div>
          </div>

          <!-- 配对码显示 -->
          <div class="pairing-code-container">
            <div class="pairing-code-display">
              <span class="code-text">{{ pairingCode || '----' }}</span>
              <el-button
                v-if="pairingCode"
                type="text"
                :icon="CopyDocument"
                @click="copyPairingCode"
                class="copy-btn"
                title="复制配对码"
              />
            </div>
          </div>
        </div>

        <!-- 配对码输入区域 -->
        <div class="input-section">
          <div class="input-group">
            <el-input
              v-model="inputPairingCode"
              placeholder="输入4位配对码"
              maxlength="4"
              class="code-input"
              @keyup.enter="handleConnectByCode"
            />
            <el-button
              type="primary"
              @click="handleConnectByCode"
              :loading="connecting"
              :disabled="inputPairingCode.length !== 4"
              :icon="Connection"
              circle
              class="connect-btn"
              title="连接设备"
            />
          </div>
        </div>

        <!-- 分割线 -->
        <div class="divider"></div>

        <!-- 附近的设备 -->
        <div class="nearby-devices-section">
          <h4 class="section-title">附近的设备:</h4>
          <div class="device-list">
            <div v-if="availableDevices.length === 0" class="empty-devices">
              <el-icon class="empty-icon"><Monitor /></el-icon>
              <span>暂无发现设备</span>
            </div>
            <div
              v-for="device in availableDevices"
              :key="device.id"
              class="device-item"
              @click="connectToDevice(device)"
            >
              <div class="device-info">
                <el-icon class="device-icon">
                  <Monitor v-if="device.type === 'desktop'" />
                  <Iphone v-else />
                </el-icon>
                <div class="device-details">
                  <span class="device-name">{{ device.name }}</span>
                  <span class="device-type">{{ getDeviceTypeText(device.type) }}</span>
                </div>
              </div>
              <el-button
                type="primary"
                size="small"
                :icon="Connection"
                circle
                class="device-connect-btn"
                :loading="connectingDevices.has(device.id)"
              />
            </div>
          </div>
        </div>

        <!-- 连接状态 -->
        <div v-if="connectionStatus" class="connection-status">
          <el-alert
            :title="connectionStatus.message"
            :type="connectionStatus.type"
            :closable="false"
            show-icon
            size="small"
          />
        </div>

        <!-- 底部操作 -->
        <div class="card-footer">
          <el-button @click="handleRefreshPairing" :loading="refreshing" size="small">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Monitor, CopyDocument, Refresh, Close, Connection, Iphone } from '@element-plus/icons-vue'
import { useDeviceStore, useWebSocketStore, useMessageStore, useAppStore } from '../store'
import { useDevicePairing } from '../composables/useDevicePairing'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Store
const deviceStore = useDeviceStore()
const wsStore = useWebSocketStore()
const messageStore = useMessageStore()
const appStore = useAppStore()
const router = useRouter()

// 使用设备配对composable - 复用HomePage逻辑
const {
  inputPairingCode,
  refreshing,
  connecting,
  connectingDevices,
  currentDevice,
  pairingCode,
  generateQRCode,
  refreshPairingCode,
  connectByCode,
  connectToDevice,
  handleWebSocketMessage,
  copyPairingCode
} = useDevicePairing()

// 响应式数据
const visible = ref(props.modelValue)
const qrCodeRef = ref(null)
const connectionStatus = ref(null)

// 计算属性
const availableDevices = computed(() => deviceStore.availableDevices)

// 监听props变化
watch(() => props.modelValue, async (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetStatus()

    // 直接生成二维码，如果没有配对码会自动处理
    await nextTick()
    handleGenerateQRCode()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听配对码变化，自动生成二维码
watch(pairingCode, (newCode) => {
  if (newCode && visible.value) {
    nextTick(() => {
      handleGenerateQRCode()
    })
  }
})

// 方法
const handleClose = () => {
  visible.value = false
  resetStatus()
}

const handleOverlayClick = () => {
  handleClose()
}

const resetStatus = () => {
  inputPairingCode.value = ''
  connectionStatus.value = null
}

const getDeviceTypeText = (type) => {
  const typeMap = {
    'desktop': '桌面设备',
    'mobile': '移动设备',
    'tablet': '平板设备'
  }
  return typeMap[type] || '未知设备'
}

// 包装composable的方法
const handleGenerateQRCode = () => {
  generateQRCode(qrCodeRef.value)
}

const handleRefreshPairing = async () => {
  await refreshPairingCode()
  await nextTick()
  handleGenerateQRCode()
}

const handleConnectByCode = async () => {
  await connectByCode()

  connectionStatus.value = {
    type: 'info',
    message: '正在连接设备...'
  }

  // 设置超时
  setTimeout(() => {
    if (connecting.value) {
      connectionStatus.value = {
        type: 'error',
        message: '连接超时，请检查配对码是否正确'
      }
    }
  }, 10000)
}

// 包装WebSocket消息处理
const handleDialogWebSocketMessage = (message) => {
  const connectedDevice = handleWebSocketMessage(message)

  // 处理连接成功的情况
  if (connectedDevice) {
    connectionStatus.value = {
      type: 'success',
      message: `成功连接到设备：${connectedDevice.name}`
    }

    // 设置活动设备并跳转
    messageStore.setActiveDevice(connectedDevice.id)

    setTimeout(() => {
      handleClose()
      router.push(`/transfer/${connectedDevice.id}`)
    }, 1500)
    return
  }

  // 处理错误情况
  if (message.type === 'error' && visible.value) {
    if (message.data.type === 'code_not_found') {
      connectionStatus.value = {
        type: 'error',
        message: '配对码不存在或已过期'
      }
    } else if (message.data.type === 'device_not_found') {
      connectionStatus.value = {
        type: 'error',
        message: '目标设备已离线'
      }
    } else {
      connectionStatus.value = {
        type: 'error',
        message: message.data.message || '连接失败'
      }
    }
  }
}

// 生命周期
onMounted(() => {
  // 监听WebSocket消息
  wsStore.onMessage(handleDialogWebSocketMessage)
})
</script>

<style scoped>
/* 导入主页样式 */
@import '../assets/styles/home-new.css';

/* 遮罩层 */
.pairing-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

/* 主卡片 - 使用与主页相同的device-card样式 */
.pairing-dialog-card {
  width: 380px;
  max-height: 85vh;
  overflow: visible; /* 移除整体滚动条 */
  margin: 20px;
  animation: dialogSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color, #14a19b);
}

/* 关闭按钮 */
.close-btn {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: #dc2626 !important;
  color: #dc2626 !important;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.close-btn {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: #dc2626 !important;
  color: #dc2626 !important;
}

/* 卡片内容 */
.card-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0; /* 允许flex子元素收缩 */
}

/* 二维码/配对码区域 - 优化对话框尺寸 */
.qr-section {
  margin-bottom: 16px; /* 减少底部间距 */
  flex-shrink: 0; /* 防止被压缩 */
}

/* 二维码容器 */
.qr-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 12px; /* 减少与配对码的间距 */
}

/* 对话框中的二维码尺寸优化 */
.pairing-dialog-card .qr-code {
  /* 覆盖主页的二维码尺寸，适配对话框 */
  width: 140px !important;
  height: 140px !important;
}

.pairing-dialog-card .qr-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

/* 配对码容器 */
.pairing-code-container {
  display: flex;
  justify-content: center;
}

/* 对话框中的配对码样式优化 */
.pairing-dialog-card .pairing-code-display {
  padding: 8px 12px; /* 减少内边距 */
  font-size: 18px; /* 稍微减小字体 */
}

/* 自定义样式补充 - 主页样式无法覆盖的部分 */
.input-section {
  margin-bottom: 16px; /* 减少间距 */
  flex-shrink: 0; /* 防止被压缩 */
}

.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.connect-btn {
  width: 40px;
  height: 40px;
}

.divider {
  height: 1px;
  background: var(--border-color, #e5e7eb);
  margin: 16px 0; /* 减少上下间距 */
  flex-shrink: 0; /* 防止被压缩 */
}

.nearby-devices-section {
  margin-bottom: 12px; /* 减少间距 */
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
}

.section-title {
  flex-shrink: 0; /* 防止标题被压缩 */
  margin-bottom: 8px; /* 减少与列表的间距 */
}

/* 设备列表容器优化 */
.pairing-dialog-card .device-list {
  max-height: 180px; /* 减少最大高度 */
  overflow-y: auto; /* 只在内容超出时显示滚动条 */
  flex: 1;
  min-height: 0; /* 移除最小高度，让它根据内容自适应 */
  /* 移除主页样式中可能存在的边框和背景 */
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

/* 确保滚动条样式正确 */
.pairing-dialog-card .device-list::-webkit-scrollbar {
  width: 6px;
}

.pairing-dialog-card .device-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.pairing-dialog-card .device-list::-webkit-scrollbar-thumb {
  background: rgba(20, 161, 155, 0.3);
  border-radius: 3px;
}

.pairing-dialog-card .device-list::-webkit-scrollbar-thumb:hover {
  background: rgba(20, 161, 155, 0.5);
}

/* 暗黑主题下的滚动条 */
.dark-theme .pairing-dialog-card .device-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark-theme .pairing-dialog-card .device-list::-webkit-scrollbar-thumb {
  background: rgba(20, 161, 155, 0.4);
}

.dark-theme .pairing-dialog-card .device-list::-webkit-scrollbar-thumb:hover {
  background: rgba(20, 161, 155, 0.6);
}

.connection-status {
  margin-top: 12px; /* 减少间距 */
  flex-shrink: 0; /* 防止被压缩 */
}

.card-footer {
  display: flex;
  justify-content: center;
  margin-top: 16px; /* 减少间距 */
  padding-top: 12px; /* 减少内边距 */
  border-top: 1px solid var(--border-color, #e5e7eb);
  flex-shrink: 0; /* 防止被压缩 */
}

/* 空设备状态优化 */
.pairing-dialog-card .empty-devices {
  padding: 30px 20px; /* 减少内边距 */
  min-height: 80px; /* 减少最小高度 */
}

/* 暗黑主题下的关闭按钮 */
.dark-theme .close-btn {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
  color: #ef4444 !important;
}

.dark-theme .close-btn:hover {
  background: rgba(239, 68, 68, 0.3) !important;
  border-color: #dc2626 !important;
  color: #dc2626 !important;
}

/* 输入区域 */
.input-section {
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.code-input {
  flex: 1;
}

.code-input :deep(.el-input__inner) {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
  font-family: 'Courier New', monospace;
}

.connect-btn {
  width: 40px;
  height: 40px;
  background: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.connect-btn:hover {
  background: var(--el-color-primary-dark-2) !important;
  border-color: var(--el-color-primary-dark-2) !important;
}

/* 分割线 */
.divider {
  height: 1px;
  background: var(--el-border-color-light);
  margin: 20px 0;
}

/* 附近设备区域 */
.nearby-devices {
  margin-bottom: 16px;
}




</style>
