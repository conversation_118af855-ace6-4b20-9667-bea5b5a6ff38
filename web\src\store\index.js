import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 设备状态管理
export const useDeviceStore = defineStore('device', () => {
  const currentDevice = ref({
    name: '',
    type: '',
    ip: '',
    id: ''
  })
  
  const availableDevices = ref([])
  const pairedDevices = ref([])
  const connectedDevices = ref([])
  const pairingCode = ref('')
  
  const isLanMode = ref(true)
  const discovering = ref(false)
  
  // 计算属性
  const hasConnectedDevices = computed(() => connectedDevices.value.length > 0)
  
  // 方法
  const updateCurrentDevice = (device) => {
    currentDevice.value = { ...device }
  }
  
  const addAvailableDevice = (device) => {
    const existingIndex = availableDevices.value.findIndex(d => d.id === device.id)
    if (existingIndex > -1) {
      availableDevices.value[existingIndex] = device
    } else {
      availableDevices.value.push(device)
    }
  }
  
  const removeAvailableDevice = (deviceId) => {
    const index = availableDevices.value.findIndex(d => d.id === deviceId)
    if (index > -1) {
      availableDevices.value.splice(index, 1)
    }
  }
  
  const addConnectedDevice = (device) => {
    const existingIndex = connectedDevices.value.findIndex(d => d.id === device.id)
    if (existingIndex === -1) {
      connectedDevices.value.push(device)
    }
    
    // 同时添加到已配对设备
    const pairedIndex = pairedDevices.value.findIndex(d => d.id === device.id)
    if (pairedIndex === -1) {
      pairedDevices.value.push({
        ...device,
        lastSeen: Date.now()
      })
      savePairedDevices()
    }
  }
  
  const removeConnectedDevice = (deviceId) => {
    const index = connectedDevices.value.findIndex(d => d.id === deviceId)
    if (index > -1) {
      connectedDevices.value.splice(index, 1)
    }
  }
  
  const loadPairedDevices = () => {
    try {
      const saved = localStorage.getItem('pairedDevices')
      if (saved) {
        pairedDevices.value = JSON.parse(saved)
      }
    } catch (error) {
      pairedDevices.value = []
    }
  }
  
  const savePairedDevices = () => {
    try {
      localStorage.setItem('pairedDevices', JSON.stringify(pairedDevices.value))
    } catch (error) {
      // 保存失败，忽略错误
    }
  }
  
  const removePairedDevice = (deviceId) => {
    const index = pairedDevices.value.findIndex(d => d.id === deviceId)
    if (index > -1) {
      pairedDevices.value.splice(index, 1)
      savePairedDevices()
    }
  }

  // 💾 连接状态持久化方法
  const saveConnectionState = () => {
    try {
      const connectionState = {
        connectedDevices: connectedDevices.value,
        currentDevice: currentDevice.value,
        timestamp: Date.now()
      }
      localStorage.setItem('connectionState', JSON.stringify(connectionState))
    } catch (error) {
      console.error('保存连接状态失败:', error)
    }
  }

  const loadConnectionState = () => {
    try {
      const saved = localStorage.getItem('connectionState')
      if (saved) {
        const state = JSON.parse(saved)
        // 检查状态是否在30分钟内（避免加载过期状态）
        const thirtyMinutes = 30 * 60 * 1000
        if (Date.now() - state.timestamp < thirtyMinutes) {
          connectedDevices.value = state.connectedDevices || []
          if (state.currentDevice && state.currentDevice.id) {
            currentDevice.value = state.currentDevice
          }
          return true
        } else {
          // 清除过期状态
          clearConnectionState()
        }
      }
    } catch (error) {
      console.error('加载连接状态失败:', error)
    }
    return false
  }

  const clearConnectionState = () => {
    try {
      localStorage.removeItem('connectionState')
    } catch (error) {
      console.error('清除连接状态失败:', error)
    }
  }

  // 重写添加和移除连接设备的方法，添加持久化
  const addConnectedDeviceWithPersist = (device) => {
    addConnectedDevice(device)
    saveConnectionState()
  }

  const removeConnectedDeviceWithPersist = (deviceId) => {
    removeConnectedDevice(deviceId)
    saveConnectionState()
  }

  return {
    // 状态
    currentDevice,
    availableDevices,
    pairedDevices,
    connectedDevices,
    pairingCode,
    isLanMode,
    discovering,

    // 计算属性
    hasConnectedDevices,

    // 方法
    updateCurrentDevice,
    addAvailableDevice,
    removeAvailableDevice,
    addConnectedDevice: addConnectedDeviceWithPersist,
    removeConnectedDevice: removeConnectedDeviceWithPersist,
    loadPairedDevices,
    savePairedDevices,
    removePairedDevice,

    // 💾 连接状态持久化方法
    saveConnectionState,
    loadConnectionState,
    clearConnectionState
  }
})

// 消息状态管理
export const useMessageStore = defineStore('message', () => {
  const messages = ref([])
  const activeDeviceId = ref('')

  // 计算属性 - 获取与当前活动设备相关的所有消息
  const currentMessages = computed(() => {
    if (!activeDeviceId.value) return []
    return messages.value.filter(msg => {
      // 显示与活动设备相关的所有消息：
      // 1. 发送给活动设备的消息（msg.deviceId === activeDeviceId 且 !msg.isRemote）
      // 2. 从活动设备接收的消息（msg.deviceId === activeDeviceId 且 msg.isRemote）
      return msg.deviceId === activeDeviceId.value
    })
  })

  // 方法
  const addMessage = (message) => {
    const messageWithId = {
      ...message,
      id: message.id || Date.now() + Math.random(),
      timestamp: message.timestamp || new Date(),
      deviceId: message.deviceId || activeDeviceId.value
    }

    // 🔄 消息去重：检查是否已存在相同的文件消息
    if (message.type === 'file' && message.file) {
      const isDuplicate = messages.value.some(existingMsg =>
        existingMsg.type === 'file' &&
        existingMsg.file &&
        existingMsg.file.id === message.file.id &&
        existingMsg.deviceId === messageWithId.deviceId &&
        existingMsg.isRemote === message.isRemote &&
        Math.abs(new Date(existingMsg.timestamp) - new Date(messageWithId.timestamp)) < 5000 // 5秒内的重复消息
      )

      if (isDuplicate) {
        console.log('检测到重复文件消息，跳过添加:', message.file.name)
        return
      }
    }

    messages.value.push(messageWithId)

    // 保存消息到localStorage
    saveMessagesToStorage()
  }

  const setActiveDevice = (deviceId) => {
    activeDeviceId.value = deviceId
    // 保存当前活动设备ID
    saveActiveDeviceToStorage()
  }

  const clearMessages = (deviceId = null) => {
    if (deviceId) {
      messages.value = messages.value.filter(msg => msg.deviceId !== deviceId)
      // 从localStorage中删除该设备的消息
      clearDeviceMessagesFromStorage(deviceId)
    } else {
      messages.value = []
      // 清空所有消息
      clearAllMessagesFromStorage()
    }
  }

  const getMessagesForDevice = (deviceId) => {
    return messages.value.filter(msg => msg.deviceId === deviceId)
  }

  // 💾 持久化存储方法
  const saveMessagesToStorage = () => {
    try {
      // 按设备IP分组存储消息
      const messagesByDevice = {}
      messages.value.forEach(msg => {
        const deviceId = msg.deviceId
        if (!messagesByDevice[deviceId]) {
          messagesByDevice[deviceId] = []
        }
        messagesByDevice[deviceId].push({
          ...msg,
          timestamp: msg.timestamp instanceof Date ? msg.timestamp.toISOString() : msg.timestamp
        })
      })

      localStorage.setItem('chatMessages', JSON.stringify(messagesByDevice))
    } catch (error) {
      console.error('保存消息失败:', error)
    }
  }

  const saveActiveDeviceToStorage = () => {
    try {
      localStorage.setItem('activeDeviceId', activeDeviceId.value)
    } catch (error) {
      console.error('保存活动设备ID失败:', error)
    }
  }

  const loadMessagesFromStorage = () => {
    try {
      const saved = localStorage.getItem('chatMessages')
      if (saved) {
        const messagesByDevice = JSON.parse(saved)
        const allMessages = []

        Object.values(messagesByDevice).forEach(deviceMessages => {
          deviceMessages.forEach(msg => {
            allMessages.push({
              ...msg,
              timestamp: new Date(msg.timestamp)
            })
          })
        })

        // 按时间戳排序
        allMessages.sort((a, b) => a.timestamp - b.timestamp)
        messages.value = allMessages
      }
    } catch (error) {
      console.error('加载消息失败:', error)
      messages.value = []
    }
  }

  const loadActiveDeviceFromStorage = () => {
    try {
      const saved = localStorage.getItem('activeDeviceId')
      if (saved) {
        activeDeviceId.value = saved
      }
    } catch (error) {
      console.error('加载活动设备ID失败:', error)
    }
  }

  const clearDeviceMessagesFromStorage = (deviceId) => {
    try {
      const saved = localStorage.getItem('chatMessages')
      if (saved) {
        const messagesByDevice = JSON.parse(saved)
        delete messagesByDevice[deviceId]
        localStorage.setItem('chatMessages', JSON.stringify(messagesByDevice))
      }
    } catch (error) {
      console.error('清除设备消息失败:', error)
    }
  }

  const clearAllMessagesFromStorage = () => {
    try {
      localStorage.removeItem('chatMessages')
    } catch (error) {
      console.error('清空所有消息失败:', error)
    }
  }

  // 获取按IP分组的消息历史
  const getMessagesByIP = () => {
    try {
      const saved = localStorage.getItem('chatMessages')
      return saved ? JSON.parse(saved) : {}
    } catch (error) {
      console.error('获取消息历史失败:', error)
      return {}
    }
  }

  // 清除指定IP的消息历史
  const clearMessagesForIP = (deviceId) => {
    clearMessages(deviceId)
  }

  return {
    // 状态
    messages,
    activeDeviceId,

    // 计算属性
    currentMessages,

    // 方法
    addMessage,
    setActiveDevice,
    clearMessages,
    getMessagesForDevice,

    // 💾 持久化方法
    loadMessagesFromStorage,
    loadActiveDeviceFromStorage,
    saveMessagesToStorage,
    saveActiveDeviceToStorage,
    getMessagesByIP,
    clearMessagesForIP
  }
})

// WebSocket 状态管理
export const useWebSocketStore = defineStore('websocket', () => {
  const websocket = ref(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)
  const messageCallback = ref(null) // 存储消息回调函数
  const lastUrl = ref('') // 存储最后连接的URL，用于重连

  const connect = (url) => {
    lastUrl.value = url // 保存URL用于重连
    return new Promise((resolve, reject) => {
      try {
        websocket.value = new WebSocket(url)

        websocket.value.onopen = () => {
          isConnected.value = true
          reconnectAttempts.value = 0

          // 重新设置消息处理器
          if (messageCallback.value) {
            setupMessageHandler()
          }

          // 通知重连成功，需要重新注册设备
          const wasReconnection = reconnectAttempts.value > 0
          if (wasReconnection) {
            // 重置重连次数后触发重新注册
            setTimeout(() => {
              if (window.reRegisterDevice) {
                window.reRegisterDevice()
              }
            }, 100)
          }

          resolve()
        }

        websocket.value.onclose = (event) => {
          isConnected.value = false

          // 如果不是正常关闭，尝试重连
          if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts.value) {
            reconnectAttempts.value++
            setTimeout(() => {
              connect(lastUrl.value)
            }, 3000) // 3秒后重连
          }
        }

        websocket.value.onerror = (error) => {
          reject(error)
        }

        // 如果已经有消息回调，立即设置
        if (messageCallback.value) {
          setupMessageHandler()
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  // 设置消息处理器的内部方法
  const setupMessageHandler = () => {
    if (websocket.value && messageCallback.value) {
      websocket.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          messageCallback.value(data)
        } catch (error) {
          // 消息解析错误，忽略该消息
        }
      }
    }
  }
  
  const disconnect = () => {
    if (websocket.value) {
      websocket.value.close()
      websocket.value = null
      isConnected.value = false
    }
  }
  
  const send = (data) => {
    if (websocket.value && isConnected.value) {
      websocket.value.send(JSON.stringify(data))
      return true
    }
    return false
  }
  
  const onMessage = (callback) => {
    messageCallback.value = callback

    // 如果WebSocket已经连接，立即设置消息处理器
    if (websocket.value) {
      setupMessageHandler()
    }
  }

  // 手动重连
  const reconnect = () => {
    if (lastUrl.value) {
      reconnectAttempts.value = 0
      return connect(lastUrl.value)
    }
    return Promise.reject('没有可用的连接URL')
  }
  
  return {
    // 状态
    websocket,
    isConnected,
    reconnectAttempts,
    maxReconnectAttempts,
    
    // 方法
    connect,
    disconnect,
    send,
    onMessage,
    reconnect
  }
})

// 应用全局状态管理
export const useAppStore = defineStore('app', () => {
  const isDarkMode = ref(false)
  const activeFileType = ref('all')
  const isSidebarOpen = ref(false) // 移动端侧边栏状态
  
  // 主题切换
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
    document.body.classList.toggle('dark-theme', isDarkMode.value)
    localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  }
  
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme === 'dark') {
      isDarkMode.value = true
      document.body.classList.add('dark-theme')
    }
  }
  
  // 侧边栏控制
  const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value
  }
  
  const closeSidebar = () => {
    isSidebarOpen.value = false
  }
  
  return {
    // 状态
    isDarkMode,
    activeFileType,
    isSidebarOpen,
    
    // 方法
    toggleTheme,
    initTheme,
    toggleSidebar,
    closeSidebar
  }
}) 