<template>
  <div class="file-message" :class="{ 'is-remote': isRemote, 'is-local': !isRemote }">
    <!-- 设备图标 -->
    <div class="device-icon">
      <el-icon size="20" :color="getDeviceIconColor()">
        <component :is="getDeviceIcon()" />
      </el-icon>
    </div>

    <!-- 图片消息 - 微信风格直接显示 -->
    <div v-if="message.type === 'file' && isImageFile(getFileName())" class="image-message">
      <div class="image-container" @click="previewImage">
        <img
          :src="getImageUrl()"
          :alt="getFileName()"
          @load="onImageLoad"
          @error="onImageError"
          class="message-image"
        />
        <div class="image-overlay">
          <div class="image-info">
            <span class="image-name">{{ getFileName() }}</span>
            <span class="image-size">{{ formatFileSize(getFileSize()) }}</span>
          </div>
          <div class="image-actions">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleDownload"
              :icon="Download"
              circle
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 视频消息 - 微信风格直接显示 -->
    <div v-else-if="message.type === 'file' && isVideoFile(getFileName())" class="video-message">
      <div class="video-container" @click="playVideo">
        <video
          ref="videoElement"
          :src="getVideoUrl()"
          :poster="getVideoPoster()"
          class="message-video"
          preload="metadata"
          @loadedmetadata="onVideoLoad"
          @error="onVideoError"
        />
        <div class="video-overlay">
          <div class="play-button">
            <el-icon size="48" color="white">
              <VideoPlay />
            </el-icon>
          </div>
          <div class="video-info">
            <span class="video-name">{{ getFileName() }}</span>
            <span class="video-size">{{ formatFileSize(getFileSize()) }}</span>
            <span v-if="videoDuration" class="video-duration">{{ formatDuration(videoDuration) }}</span>
          </div>
          <div class="video-actions">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleDownload"
              :icon="Download"
              circle
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 其他文件 - 使用气泡样式 -->
    <div v-else-if="message.type === 'file'" class="message-bubble file-bubble">
      <div class="file-content">
        <!-- 文件图标区域 -->
        <div class="file-icon-section">
          <div class="file-type-icon">
            <el-icon size="32" :color="getFileIconColor()">
              <component :is="getFileIcon()" />
            </el-icon>
          </div>
        </div>

        <!-- 文件名区域 -->
        <div class="file-info-section">
          <div class="file-name">
            {{ getFileName() }}
          </div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(getFileSize()) }}</span>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="file-actions-section">
          <el-button
            type="primary"
            size="small"
            @click="handleDownload"
            :icon="Download"
            class="download-btn"
            circle
          />
          <el-button
            type="danger"
            size="small"
            @click="handleDelete"
            :icon="Delete"
            class="delete-btn"
            circle
          />
        </div>
      </div>
    </div>

    <!-- 文本消息 -->
    <div v-else-if="message.type === 'text'" class="message-bubble text-bubble">
      <div class="text-content">
        {{ message.content }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import {
  Document, Picture, VideoPlay, Download, View, Delete,
  Files, Paperclip, Monitor, Iphone, Cellphone,
  FolderOpened, Folder, Setting
} from '@element-plus/icons-vue'
import { formatFileSize, formatTime } from '../utils/file.js'

export default {
  name: 'FileMessage',
  components: {
    Document,
    Picture,
    VideoPlay,
    Download,
    View,
    Delete,
    Files,
    Paperclip,
    Monitor,
    Iphone,
    Cellphone,
    FolderOpened,
    Folder,
    Setting
  },
  props: {
    message: {
      type: Object,
      required: true
    },
    isRemote: {
      type: Boolean,
      default: false
    }
  },
  emits: ['download'],
  setup(props, { emit }) {
    const showImagePreview = ref(false)
    const videoElement = ref(null)
    const videoDuration = ref(0)

    // 安全获取文件名
    const getFileName = () => {
      return props.message?.file?.name || props.message?.content?.name || '未知文件'
    }

    // 安全获取文件大小
    const getFileSize = () => {
      return props.message?.file?.size || props.message?.content?.size || 0
    }

    // 安全获取文件ID
    const getFileId = () => {
      return props.message?.file?.id || props.message?.content?.id || ''
    }

    // 获取图片URL
    const getImageUrl = () => {
      const fileId = getFileId()
      if (fileId) {
        return `/api/file/download/${fileId}`
      }
      // 如果是本地文件，可能需要使用blob URL
      return props.message?.file?.url || props.message?.content?.url || ''
    }

    // 获取视频URL
    const getVideoUrl = () => {
      const fileId = getFileId()
      if (fileId) {
        return `/api/file/download/${fileId}`
      }
      return props.message?.file?.url || props.message?.content?.url || ''
    }

    // 获取视频封面
    const getVideoPoster = () => {
      // 可以返回视频的缩略图URL，如果有的话
      return props.message?.file?.poster || props.message?.content?.poster || ''
    }

    // 获取设备图标
    const getDeviceIcon = () => {
      // 根据消息来源判断设备类型
      // 这里可以根据实际的设备信息字段来判断
      const deviceType = props.message?.deviceType || 'desktop'

      switch (deviceType) {
        case 'mobile':
        case 'phone':
        case 'android':
        case 'ios':
          return Iphone
        case 'tablet':
        case 'ipad':
        case 'android_tablet':
          return Cellphone // 使用Cellphone图标代表平板
        case 'desktop':
        case 'pc':
        case 'computer':
        case 'laptop':
        case 'windows':
        case 'macos':
        case 'linux':
        default:
          return Monitor
      }
    }

    // 获取设备图标颜色
    const getDeviceIconColor = () => {
      return props.isRemote ? '#909399' : '#14a19b'
    }

    // 获取文件图标 - 更详细的文件类型支持
    const getFileIcon = () => {
      const fileName = getFileName()
      if (!fileName) return Files

      const ext = fileName.toLowerCase().split('.').pop()

      switch (ext) {
        // 图片文件
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
        case 'svg':
          return Picture

        // 视频文件
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'flv':
        case 'mkv':
        case 'webm':
          return VideoPlay

        // 音频文件
        case 'mp3':
        case 'wav':
        case 'flac':
        case 'aac':
        case 'ogg':
        case 'm4a':
          return Files

        // 文档文件
        case 'doc':
        case 'docx':
        case 'pdf':
        case 'txt':
        case 'rtf':
          return Document
        case 'ppt':
        case 'pptx':
          return FolderOpened
        case 'xls':
        case 'xlsx':
          return Document

        // 压缩文件
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
          return Folder

        // 可执行文件
        case 'exe':
        case 'msi':
        case 'dmg':
        case 'pkg':
        case 'deb':
        case 'rpm':
        case 'apk':
          return Setting

        // 其他文件
        default:
          return Files
      }
    }

    // 获取文件图标颜色
    const getFileIconColor = () => {
      const fileName = getFileName()
      if (!fileName) return '#909399'

      const ext = fileName.toLowerCase().split('.').pop()

      switch (ext) {
        // 图片文件 - 绿色
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
        case 'svg':
          return '#67C23A'

        // 视频文件 - 橙色
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'flv':
        case 'mkv':
        case 'webm':
          return '#E6A23C'

        // 音频文件 - 紫色
        case 'mp3':
        case 'wav':
        case 'flac':
        case 'aac':
        case 'ogg':
        case 'm4a':
          return '#9C27B0'

        // Word文档 - 蓝色
        case 'doc':
        case 'docx':
          return '#2196F3'

        // PDF - 红色
        case 'pdf':
          return '#F44336'

        // PPT - 橙红色
        case 'ppt':
        case 'pptx':
          return '#FF5722'

        // Excel - 绿色
        case 'xls':
        case 'xlsx':
          return '#4CAF50'

        // 文本文件 - 灰色
        case 'txt':
        case 'rtf':
          return '#607D8B'

        // 压缩文件 - 棕色
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
          return '#795548'

        // 可执行文件 - 深灰色
        case 'exe':
        case 'msi':
        case 'dmg':
        case 'pkg':
        case 'deb':
        case 'rpm':
        case 'apk':
          return '#424242'

        // 其他文件 - 默认灰色
        default:
          return '#909399'
      }
    }
    
    const isImageFile = (fileName) => {
      if (!fileName) return false
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      return imageExtensions.some(ext => fileName.endsWith(ext))
    }
    
    const isVideoFile = (fileName) => {
      if (!fileName) return false
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
      return videoExtensions.some(ext => fileName.endsWith(ext))
    }
    
    const isDocumentFile = (fileName) => {
      if (!fileName) return false
      const docExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.xls', '.xlsx', '.ppt', '.pptx']
      return docExtensions.some(ext => fileName.endsWith(ext))
    }
    
    // 图片相关方法
    const previewImage = () => {
      // 可以在这里实现图片预览功能，比如打开大图查看
      console.log('预览图片:', getFileName())
    }

    const onImageLoad = () => {
      console.log('图片加载成功')
    }

    const onImageError = () => {
      console.log('图片加载失败')
    }

    // 视频相关方法
    const playVideo = () => {
      if (videoElement.value) {
        if (videoElement.value.paused) {
          videoElement.value.play()
        } else {
          videoElement.value.pause()
        }
      }
    }

    const onVideoLoad = () => {
      if (videoElement.value) {
        videoDuration.value = videoElement.value.duration
      }
      console.log('视频加载成功')
    }

    const onVideoError = () => {
      console.log('视频加载失败')
    }

    // 格式化视频时长
    const formatDuration = (seconds) => {
      if (!seconds || isNaN(seconds)) return '00:00'

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    // 检测文件名是否被截断（简单方法：检查长度）
    const isFileNameTruncated = (fileName) => {
      if (!fileName) return false
      // 如果文件名超过30个字符，可能会被截断显示
      return fileName.length > 30
    }

    const handleDownload = () => {
      const fileData = props.message?.file || props.message?.content
      if (fileData) {
        emit('download', fileData)
      }
    }

    const handleDelete = () => {
      const fileData = props.message?.file || props.message?.content
      if (fileData) {
        emit('delete', fileData)
      }
    }

    return {
      // 响应式数据
      showImagePreview,
      videoElement,
      videoDuration,

      // 工具方法
      formatFileSize,
      formatTime,
      isImageFile,
      isVideoFile,
      getFileName,
      getFileSize,
      getFileId,
      getFileIcon,
      getFileIconColor,
      getDeviceIcon,
      getDeviceIconColor,

      // 媒体相关方法
      getImageUrl,
      getVideoUrl,
      getVideoPoster,
      previewImage,
      onImageLoad,
      onImageError,
      playVideo,
      onVideoLoad,
      onVideoError,
      formatDuration,

      // 操作方法
      handleDownload,
      handleDelete,
      isFileNameTruncated,

      // 图标组件
      Document,
      Picture,
      VideoPlay,
      Download,
      View,
      Delete
    }
  }
}
</script>

<style scoped>
@import '../assets/styles/file-message.css';
@import '../assets/styles/media-message.css';
</style>