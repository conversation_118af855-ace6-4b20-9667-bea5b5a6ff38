import { createRouter, createWebHistory } from 'vue-router'
import { useDeviceStore } from '../store/index.js'

// 路由组件
const HomePage = () => import('../views/HomePage.vue')
const TransferPage = () => import('../views/TransferPage.vue')
const BubbleTest = () => import('../views/BubbleTest.vue')


const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
    meta: {
      title: 'WSShare - 设备连接'
    }
  },
  {
    path: '/transfer',
    name: 'Transfer',
    component: TransferPage,
    meta: {
      title: 'WSShare - 文件传输',
      requiresConnection: true
    },
    beforeEnter: (to, from, next) => {
      const deviceStore = useDeviceStore()

      // 检查当前连接状态或尝试从localStorage恢复
      if (deviceStore.hasConnectedDevices) {
        next()
      } else {
        // 尝试从localStorage恢复连接状态
        const hasRestoredState = deviceStore.loadConnectionState()
        if (hasRestoredState && deviceStore.hasConnectedDevices) {
          next()
        } else {
          next('/')
        }
      }
    }
  },
  {
    path: '/transfer/:deviceId',
    name: 'TransferWithDevice',
    component: TransferPage,
    meta: {
      title: 'WSShare - 文件传输',
      requiresConnection: true
    },
    beforeEnter: (to, from, next) => {
      const deviceStore = useDeviceStore()
      const deviceId = to.params.deviceId

      // 检查当前连接状态
      let hasDevice = deviceStore.connectedDevices.some(device => device.id === deviceId)

      if (!hasDevice) {
        // 尝试从localStorage恢复连接状态
        const hasRestoredState = deviceStore.loadConnectionState()
        if (hasRestoredState) {
          hasDevice = deviceStore.connectedDevices.some(device => device.id === deviceId)
        }
      }

      if (hasDevice) {
        next()
      } else {
        next('/')
      }
    }
  },
  {
    path: '/bubble-test',
    name: 'BubbleTest',
    component: BubbleTest,
    meta: {
      title: 'WSShare - 气泡测试'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  next()
})

export default router 