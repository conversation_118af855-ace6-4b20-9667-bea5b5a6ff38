package handler

import (
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"wsshare/internal/model"
	"wsshare/internal/util"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
	EnableCompression: true,
}

type WebSocketMessage struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

type DeviceManager struct {
	devices     map[string]*ConnectedDevice
	pairingCodes map[string]string // 配对码到设备ID的映射
	mutex       sync.RWMutex
}

type ConnectedDevice struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Type        string          `json:"type"`
	IP          string          `json:"ip"`
	Platform    string          `json:"platform"`
	PairingCode string          `json:"pairing_code"`
	Conn        *websocket.Conn `json:"-"`
}

var deviceManager = &DeviceManager{
	devices:      make(map[string]*ConnectedDevice),
	pairingCodes: make(map[string]string),
}

func HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Print("upgrade failed: ", err)
		return
	}
	defer conn.Close()

	// 设置读写超时和心跳
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetWriteDeadline(time.Now().Add(30 * time.Second))  // 增加写超时时间
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	var currentDevice *ConnectedDevice
	
	// 启动心跳goroutine
	done := make(chan bool)
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		
		for {
			select {
			case <-done:
				return
			case <-ticker.C:
				conn.SetWriteDeadline(time.Now().Add(30 * time.Second))  // 心跳也使用更长的超时时间
				if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					log.Printf("心跳发送失败: %v", err)
					return
				}
			}
		}
	}()

	for {
		var message WebSocketMessage
		err := conn.ReadJSON(&message)
		if err != nil {
			log.Println("read failed:", err)
			break
		}

		// 处理收到的消息

		switch message.Type {
		case "device_info":
			currentDevice = handleDeviceInfo(conn, message.Data, c.ClientIP())
			
		case "discover_devices":
			handleDiscoverDevices(conn)
			
		case "connect_request":
			handleConnectRequest(conn, message.Data)
			
		case "connect_by_code":
			handleConnectByCode(conn, message.Data)
			
		case "disconnect":
			handleDisconnect(conn, message.Data)
			
		case "get_file_list":
			handleGetFileList(conn)
			
		case "text_message":
			handleTextMessage(conn, message.Data)

		case "file_notification":
			handleFileNotification(conn, message.Data)

		default:
			log.Printf("未知消息类型: %s", message.Type)
		}
	}

	// 停止心跳
	close(done)
	
	// 连接断开时清理设备
	if currentDevice != nil {
		log.Printf("设备断开连接: %s(%s)", currentDevice.Name, currentDevice.ID)
		deviceManager.mutex.Lock()
		delete(deviceManager.devices, currentDevice.ID)
		// 清理配对码映射
		if currentDevice.PairingCode != "" {
			delete(deviceManager.pairingCodes, currentDevice.PairingCode)
		}
		deviceManager.mutex.Unlock()
		
		// 通知其他设备此设备已断开
		broadcastDeviceList()
	}
}

// 通知新文件到所有设备（除了上传者）
func notifyNewFile(file *model.File, uploaderIP string) {
	message := WebSocketMessage{
		Type: "new_file",
		Data: map[string]interface{}{
			"file":        file,
			"uploader_ip": uploaderIP,
		},
	}
	
	deviceManager.mutex.RLock()
	for _, device := range deviceManager.devices {
		// 只通知其他设备，不通知上传者自己
		if device.IP != uploaderIP {
			if err := device.Conn.WriteJSON(message); err != nil {
				log.Printf("发送文件通知失败: %v", err)
			}
		}
	}
	deviceManager.mutex.RUnlock()
}

func handleGetFileList(conn *websocket.Conn) {
	// 这里需要访问文件服务，暂时返回空列表
	response := WebSocketMessage{
		Type: "file_list",
		Data: []interface{}{},
	}
	conn.WriteJSON(response)
}

func handleTextMessage(conn *websocket.Conn, data interface{}) {
	messageData, ok := data.(map[string]interface{})
	if !ok {
		return
	}
	
	// 支持content和message两种字段
	var textContent string
	if content, exists := messageData["content"].(string); exists {
		textContent = content
	} else if message, exists := messageData["message"].(string); exists {
		textContent = message
	} else {
		return
	}

	targetDeviceID, ok := messageData["target_device"].(string)
	if !ok {
		return
	}

	deviceManager.mutex.RLock()
	targetDevice, exists := deviceManager.devices[targetDeviceID]
	
	deviceManager.mutex.RUnlock()
	
	if !exists {
		
		// 发送错误消息给发送方，提示设备已离线
		errorMsg := WebSocketMessage{
			Type: "error",
			Data: map[string]interface{}{
				"type":    "device_offline",
				"message": "目标设备已离线，请重新连接",
				"action":  "reconnect_required",
			},
		}
		conn.WriteJSON(errorMsg)
		return
	}
	
	// 向目标设备发送文字消息
	sourceDevice := findDeviceByConn(conn)
	if sourceDevice == nil {
		return
	}
	
	textMessage := WebSocketMessage{
		Type: "new_text_message",
		Data: map[string]interface{}{
			"content":        textContent,
			"from_device":    sourceDevice.Name,
			"from_device_id": sourceDevice.ID,  // 添加设备ID
			"timestamp":      time.Now().Format(time.RFC3339),
		},
	}
	
	if err := targetDevice.Conn.WriteJSON(textMessage); err != nil {
		// 发送失败，可以在这里添加错误处理逻辑
	}
}

func handleDeviceInfo(conn *websocket.Conn, data interface{}, clientIP string) *ConnectedDevice {
	deviceInfo := util.GetDeviceInfo()
	
	// 生成设备ID
	deviceID := util.GenerateUUID()
	
	// 生成4位配对码
	pairingCode := generatePairingCode()
	
	// 从前端传来的数据中获取设备类型
	var deviceType = "desktop"
	var deviceName = deviceInfo["hostname"]
	
	if dataMap, ok := data.(map[string]interface{}); ok {
		if userAgent, exists := dataMap["userAgent"].(string); exists {
			// 通过 User-Agent 判断设备类型
			if isMobile(userAgent) {
				deviceType = "mobile"
				deviceName = extractMobileDeviceName(userAgent)
			}
		}
		if name, exists := dataMap["deviceName"].(string); exists && name != "" {
			deviceName = name
		}
	}
	
	device := &ConnectedDevice{
		ID:          deviceID,
		Name:        deviceName,
		Type:        deviceType,
		IP:          clientIP,
		Platform:    deviceInfo["platform"],
		PairingCode: pairingCode,
		Conn:        conn,
	}
	
	deviceManager.mutex.Lock()
	deviceManager.devices[deviceID] = device
	deviceManager.pairingCodes[pairingCode] = deviceID
	deviceManager.mutex.Unlock()
	
	// 发送设备注册成功消息，包含配对码
	response := WebSocketMessage{
		Type: "device_registered",
		Data: map[string]interface{}{
			"device_id":    deviceID,
			"pairing_code": pairingCode,
			"device_name":  deviceName,
			"device_type":  deviceType,
			"status":       "success",
		},
	}
	conn.WriteJSON(response)
	
	// 广播设备列表更新
	broadcastDeviceList()
	
	return device
}

// 生成4位随机配对码
func generatePairingCode() string {
	return util.GenerateRandomCode(4)
}

// 判断是否为移动设备
func isMobile(userAgent string) bool {
	mobileKeywords := []string{
		"Mobile", "Android", "iPhone", "iPad", "iPod", 
		"BlackBerry", "IEMobile", "Opera Mini", "webOS",
	}
	
	for _, keyword := range mobileKeywords {
		if strings.Contains(userAgent, keyword) {
			return true
		}
	}
	return false
}

// 从 User-Agent 中提取手机设备名
func extractMobileDeviceName(userAgent string) string {
	if strings.Contains(userAgent, "iPhone") {
		return "iPhone"
	} else if strings.Contains(userAgent, "iPad") {
		return "iPad"
	} else if strings.Contains(userAgent, "Android") {
		return "Android 设备"
	}
	return "移动设备"
}

func handleDiscoverDevices(conn *websocket.Conn) {
	deviceManager.mutex.RLock()
	devices := make([]*model.Device, 0, len(deviceManager.devices))
	
	for _, device := range deviceManager.devices {
		// 不包含请求者自己
		if device.Conn != conn {
			devices = append(devices, &model.Device{
				ID:       device.ID,
				Name:     device.Name,
				Type:     device.Type,
				IP:       device.IP,
				Platform: device.Platform,
				Online:   true,
			})
		}
	}
	deviceManager.mutex.RUnlock()
	
	response := WebSocketMessage{
		Type: "device_list",
		Data: devices,
	}
	conn.WriteJSON(response)
}

func handleConnectRequest(conn *websocket.Conn, data interface{}) {
	requestData, ok := data.(map[string]interface{})
	if !ok {
		return
	}

	targetDeviceID, ok := requestData["target_device"].(string)
	if !ok {
		return
	}
	
	deviceManager.mutex.RLock()
	targetDevice, exists := deviceManager.devices[targetDeviceID]
	deviceManager.mutex.RUnlock()
	
	if !exists {
		// 目标设备不存在
		response := WebSocketMessage{
			Type: "connection_failed",
			Data: map[string]string{
				"reason": "设备不存在",
			},
		}
		conn.WriteJSON(response)
		return
	}
	
	// 向目标设备发送连接请求
	sourceDevice := findDeviceByConn(conn)
	if sourceDevice == nil {
		return
	}
	
	connectionRequest := WebSocketMessage{
		Type: "connection_request",
		Data: map[string]interface{}{
			"source_device": sourceDevice,
			"message":       "请求连接到您的设备",
		},
	}
	
	if err := targetDevice.Conn.WriteJSON(connectionRequest); err == nil {
		// 自动接受连接（简化版本，实际应该让用户确认）
		acceptConnection(sourceDevice, targetDevice)
	}
}

// 处理配对码连接
func handleConnectByCode(conn *websocket.Conn, data interface{}) {
	requestData, ok := data.(map[string]interface{})
	if !ok {
		sendErrorResponse(conn, "invalid_data", "无效的请求数据")
		return
	}

	code, ok := requestData["code"].(string)
	if !ok || len(code) != 4 {
		sendErrorResponse(conn, "invalid_code", "无效的配对码")
		return
	}
	
	deviceManager.mutex.RLock()
	targetDeviceID, exists := deviceManager.pairingCodes[code]
	deviceManager.mutex.RUnlock()
	
	if !exists {
		sendErrorResponse(conn, "code_not_found", "配对码不存在或已过期")
		return
	}
	
	deviceManager.mutex.RLock()
	targetDevice, exists := deviceManager.devices[targetDeviceID]
	deviceManager.mutex.RUnlock()
	
	if !exists {
		sendErrorResponse(conn, "device_not_found", "目标设备已离线")
		return
	}
	
	// 获取源设备信息
	sourceDevice := findDeviceByConn(conn)
	if sourceDevice == nil {
		sendErrorResponse(conn, "source_not_found", "源设备信息获取失败")
		return
	}
	
	// 建立连接
	establishConnection(sourceDevice, targetDevice)
}

// 建立设备连接
func establishConnection(sourceDevice, targetDevice *ConnectedDevice) {
	// 向源设备发送连接成功消息
	sourceResponse := WebSocketMessage{
		Type: "connection_established",
		Data: &model.Device{
			ID:       targetDevice.ID,
			Name:     targetDevice.Name,
			Type:     targetDevice.Type,
			IP:       targetDevice.IP,
			Platform: targetDevice.Platform,
			Online:   true,
		},
	}
	sourceDevice.Conn.SetWriteDeadline(time.Now().Add(30 * time.Second))
	sourceErr := sourceDevice.Conn.WriteJSON(sourceResponse)
	if sourceErr != nil {
		// 发送连接失败消息到源设备
		sendErrorResponse(sourceDevice.Conn, "connection_failed", "消息发送超时")
	}
	
	// 向目标设备发送连接成功消息
	targetResponse := WebSocketMessage{
		Type: "connection_established",
		Data: &model.Device{
			ID:       sourceDevice.ID,
			Name:     sourceDevice.Name,
			Type:     sourceDevice.Type,
			IP:       sourceDevice.IP,
			Platform: sourceDevice.Platform,
			Online:   true,
		},
	}
	targetDevice.Conn.SetWriteDeadline(time.Now().Add(30 * time.Second))
	targetErr := targetDevice.Conn.WriteJSON(targetResponse)
	if targetErr != nil {
		// 发送连接失败消息到目标设备
		sendErrorResponse(targetDevice.Conn, "connection_failed", "消息发送超时")
	}
	
	// 连接建立完成
}

// 发送错误响应
func sendErrorResponse(conn *websocket.Conn, errorType, message string) {
	response := WebSocketMessage{
		Type: "error",
		Data: map[string]string{
			"type":    errorType,
			"message": message,
		},
	}
	conn.WriteJSON(response)
}

func acceptConnection(sourceDevice, targetDevice *ConnectedDevice) {
	// 向源设备发送连接成功消息
	sourceResponse := WebSocketMessage{
		Type: "connection_established",
		Data: &model.Device{
			ID:       targetDevice.ID,
			Name:     targetDevice.Name,
			Type:     targetDevice.Type,
			IP:       targetDevice.IP,
			Platform: targetDevice.Platform,
			Online:   true,
		},
	}
	if err := sourceDevice.Conn.WriteJSON(sourceResponse); err != nil {
		// 发送失败处理
	}
	
	// 向目标设备发送连接成功消息
	targetResponse := WebSocketMessage{
		Type: "connection_established",
		Data: &model.Device{
			ID:       sourceDevice.ID,
			Name:     sourceDevice.Name,
			Type:     sourceDevice.Type,
			IP:       sourceDevice.IP,
			Platform: sourceDevice.Platform,
			Online:   true,
		},
	}
	if err := targetDevice.Conn.WriteJSON(targetResponse); err != nil {
		// 发送失败处理
	}
}

func handleDisconnect(conn *websocket.Conn, data interface{}) {
	// 处理设备断开连接
	device := findDeviceByConn(conn)
	if device != nil {
		// 通知其他设备
		disconnectMessage := WebSocketMessage{
			Type: "device_disconnected",
			Data: map[string]string{
				"id": device.ID,
			},
		}
		
		deviceManager.mutex.RLock()
		for _, otherDevice := range deviceManager.devices {
			if otherDevice.Conn != conn {
				otherDevice.Conn.WriteJSON(disconnectMessage)
			}
		}
		deviceManager.mutex.RUnlock()
	}
}

func findDeviceByConn(conn *websocket.Conn) *ConnectedDevice {
	deviceManager.mutex.RLock()
	defer deviceManager.mutex.RUnlock()
	
	for _, device := range deviceManager.devices {
		if device.Conn == conn {
			return device
		}
	}
	return nil
}

func broadcastDeviceList() {
	deviceManager.mutex.RLock()
	devices := make([]*model.Device, 0, len(deviceManager.devices))
	
	for _, device := range deviceManager.devices {
		devices = append(devices, &model.Device{
			ID:       device.ID,
			Name:     device.Name,
			Type:     device.Type,
			IP:       device.IP,
			Platform: device.Platform,
			Online:   true,
		})
	}
	
	message := WebSocketMessage{
		Type: "device_list",
		Data: devices,
	}
	
	for _, device := range deviceManager.devices {
		device.Conn.WriteJSON(message)
	}
	deviceManager.mutex.RUnlock()
}

// 处理文件通知消息
func handleFileNotification(conn *websocket.Conn, data interface{}) {
	// 获取发送者设备信息
	senderDevice := findDeviceByConn(conn)
	if senderDevice == nil {
		return
	}

	// 解析消息数据，获取目标设备ID
	messageData, ok := data.(map[string]interface{})
	if !ok {
		return
	}

	targetDeviceID, ok := messageData["target_device"].(string)
	if !ok {
		// 如果没有指定目标设备，返回错误
		errorMsg := WebSocketMessage{
			Type: "error",
			Data: map[string]interface{}{
				"type":    "missing_target",
				"message": "文件发送需要指定目标设备",
			},
		}
		conn.WriteJSON(errorMsg)
		return
	}

	// 查找目标设备
	deviceManager.mutex.RLock()
	targetDevice, exists := deviceManager.devices[targetDeviceID]
	deviceManager.mutex.RUnlock()

	if !exists {
		// 目标设备不存在或已离线
		errorMsg := WebSocketMessage{
			Type: "error",
			Data: map[string]interface{}{
				"type":    "device_offline",
				"message": "目标设备已离线，请重新连接",
				"action":  "reconnect_required",
			},
		}
		conn.WriteJSON(errorMsg)
		return
	}

	// 构建文件通知消息，添加发送者信息
	fileNotification := WebSocketMessage{
		Type: "file_notification",
		Data: map[string]interface{}{
			"file":           messageData["file"],
			"senderName":     senderDevice.Name,
			"from_device":    senderDevice.Name,
			"from_device_id": senderDevice.ID,
			"timestamp":      messageData["timestamp"],
		},
	}

	// 只发送给指定的目标设备
	if err := targetDevice.Conn.WriteJSON(fileNotification); err != nil {
		// 发送失败，通知发送方
		errorMsg := WebSocketMessage{
			Type: "error",
			Data: map[string]interface{}{
				"type":    "send_failed",
				"message": "文件通知发送失败",
			},
		}
		conn.WriteJSON(errorMsg)
	}
}
