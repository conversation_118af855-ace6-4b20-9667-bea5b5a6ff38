<template>
  <el-dialog
    v-model="visible"
    title="聊天历史管理"
    width="500px"
    :before-close="handleClose"
  >
    <div class="history-manager">
      <div class="history-stats">
        <el-card class="stats-card">
          <div class="stat-item">
            <span class="stat-label">总设备数:</span>
            <span class="stat-value">{{ Object.keys(messagesByDevice).length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总消息数:</span>
            <span class="stat-value">{{ totalMessages }}</span>
          </div>
        </el-card>
      </div>

      <div class="device-list">
        <div 
          v-for="(messages, deviceId) in messagesByDevice" 
          :key="deviceId"
          class="device-item"
        >
          <div class="device-info">
            <div class="device-name">
              <el-icon><Monitor /></el-icon>
              {{ getDeviceName(deviceId) }}
            </div>
            <div class="device-stats">
              <span class="message-count">{{ messages.length }} 条消息</span>
              <span class="last-message">
                最后消息: {{ formatTime(getLastMessageTime(messages)) }}
              </span>
            </div>
          </div>
          <div class="device-actions">
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="clearDeviceHistory(deviceId)"
            >
              清除
            </el-button>
          </div>
        </div>
      </div>

      <div class="empty-state" v-if="Object.keys(messagesByDevice).length === 0">
        <el-icon class="empty-icon"><ChatDotSquare /></el-icon>
        <p>暂无聊天历史记录</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="danger" 
          @click="clearAllHistory"
          :disabled="Object.keys(messagesByDevice).length === 0"
        >
          清除全部
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, Delete, ChatDotSquare } from '@element-plus/icons-vue'
import { useMessageStore, useDeviceStore } from '../store'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Store
const messageStore = useMessageStore()
const deviceStore = useDeviceStore()

// 响应式数据
const visible = ref(props.modelValue)

// 计算属性
const messagesByDevice = computed(() => {
  return messageStore.getMessagesByIP()
})

const totalMessages = computed(() => {
  return Object.values(messagesByDevice.value).reduce((total, messages) => {
    return total + messages.length
  }, 0)
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const getDeviceName = (deviceId) => {
  const device = deviceStore.connectedDevices.find(d => d.id === deviceId) ||
                 deviceStore.pairedDevices.find(d => d.id === deviceId)
  return device ? device.name : `设备 ${deviceId.slice(0, 8)}`
}

const getLastMessageTime = (messages) => {
  if (messages.length === 0) return null
  const lastMessage = messages[messages.length - 1]
  return new Date(lastMessage.timestamp)
}

const formatTime = (time) => {
  if (!time) return '无'
  const now = new Date()
  const diff = now - time
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)} 分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)} 小时前`
  return `${Math.floor(diff / 86400000)} 天前`
}

const clearDeviceHistory = async (deviceId) => {
  try {
    await ElMessageBox.confirm(
      `确定要清除设备 "${getDeviceName(deviceId)}" 的聊天记录吗？`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    messageStore.clearMessagesForIP(deviceId)
    ElMessage.success('设备聊天记录已清除')
  } catch {
    // 用户取消
  }
}

const clearAllHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有设备的聊天记录吗？此操作不可恢复！',
      '确认清除全部',
      {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    messageStore.clearMessages()
    ElMessage.success('所有聊天记录已清除')
    handleClose()
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.history-manager {
  max-height: 400px;
  overflow-y: auto;
}

.history-stats {
  margin-bottom: 16px;
}

.stats-card {
  background: var(--el-bg-color-page);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: var(--el-text-color-regular);
}

.stat-value {
  font-weight: 600;
  color: var(--el-color-primary);
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background: var(--el-bg-color);
}

.device-info {
  flex: 1;
}

.device-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 4px;
}

.device-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.message-count {
  font-weight: 500;
}

.device-actions {
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-regular);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
