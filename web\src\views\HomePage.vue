<template>
  <div class="home-page" :class="{ 'dark-theme': appStore.isDarkMode }">
    <!-- Logo区域 - 精确按照设计方案：线条→logo图片和文字→线条 -->
    <header class="page-header">
      <!-- 主题切换按钮 -->
      <div class="theme-toggle-container">
        <el-button 
          @click="appStore.toggleTheme"
          :icon="appStore.isDarkMode ? Sunny : Moon"
          circle
          size="small"
          class="theme-toggle-btn"
        />
      </div>
      
      <div class="logo-section">
        <div class="divider"></div>
        <div class="logo-content">
          <img src="/logo.svg" alt="WSLink Logo" class="logo-icon" />
          <span class="logo-text">WSLink</span>
        </div>
        <div class="divider"></div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 设备配对区域 - 三个主要卡片横向布局 -->
      <section class="pairing-section">
        <!-- 附近的设备卡片 -->
        <div class="device-card nearby-devices-card">
          <div class="card-header">
            <div class="card-title-with-icon">
              <el-icon class="search-icon"><Search /></el-icon>
              <h3 class="card-title">附近的设备</h3>
            </div>
            <button 
              class="refresh-button"
              @click="discoverDevices"
              :disabled="deviceStore.discovering"
              title="刷新"
            >
              <el-icon :class="{ 'is-loading': deviceStore.discovering }"><Refresh /></el-icon>
            </button>
          </div>
          
          <div class="device-list">
            <div 
              v-for="device in deviceStore.availableDevices" 
              :key="device.id"
              class="device-item"
              @click="connectToDevice(device)"
            >
              <div class="device-info">
                <el-icon size="20" class="device-icon">
                  <Monitor v-if="device.type === 'desktop'" />
                  <Iphone v-else />
                </el-icon>
                <div class="device-details">
                  <div class="device-name">{{ getDeviceDisplayName(device) }}</div>
                  <div class="device-meta">
                    <span class="device-ip">{{ device.ip }}</span>
                  </div>
                </div>
              </div>
              <button 
                class="connect-button"
                :disabled="connectingDevices.has(device.id)"
              >
                {{ connectingDevices.has(device.id) ? '连接中' : '连接' }}
              </button>
            </div>
            
            <div v-if="deviceStore.availableDevices.length === 0" class="empty-state">
              <el-icon class="empty-icon"><Search /></el-icon>
              <p class="empty-text">未发现设备</p>
              <p class="empty-tip">请确保设备在同一网络下</p>
            </div>
          </div>
        </div>

        <!-- 二维码/配对码卡片 -->
        <div class="device-card pairing-card">
          <h3 class="card-title center">扫码 / 输入配对码配对</h3>
          
          <!-- 白色圆角矩形包装二维码和配对码 -->
          <div class="qr-section">
            <!-- 二维码 -->
            <div class="qr-code-container">
              <div class="qr-code" ref="qrcodeElement"></div>
            </div>
            
            <!-- 配对码显示 -->
            <div class="pairing-code-display">
              <span class="code-label">本机</span>
              <span class="pairing-code">{{ deviceStore.pairingCode || '0627' }}</span>
              <button 
                class="refresh-code-btn"
                @click="refreshPairingCode"
                :disabled="refreshing"
                title="刷新配对码"
              >
                <el-icon :class="{ 'is-loading': refreshing }"><Refresh /></el-icon>
              </button>
            </div>
          </div>
          
          <!-- 输入配对码区域 -->
          <div class="code-input-section">
            <input 
              v-model="inputPairingCode"
              placeholder="输入其他设备配对码"
              maxlength="4"
              class="code-input"
              @keyup.enter="connectByCode"
            />
            
            <button 
              class="connect-button-large"
              @click="connectByCode"
              :disabled="inputPairingCode.length !== 4"
            >
              连接
            </button>
          </div>

          <!-- 当前设备信息 -->
          <div class="current-device-section">
            <span class="device-label">当前设备:</span>
            <div class="current-device-info">
              <el-icon size="16" class="device-icon">
                <Monitor v-if="deviceStore.currentDevice.type === 'desktop'" />
                <Iphone v-else />
              </el-icon>
              <span class="device-name">{{ getDeviceDisplayName(deviceStore.currentDevice) }}</span>
              <div class="online-indicator"></div>
              <span class="system-info">{{ getSystemInfo() }}</span>
            </div>
          </div>
        </div>

        <!-- 历史设备卡片 -->
        <div class="device-card history-devices-card">
          <div class="card-header">
            <div class="card-title-with-icon">
              <el-icon class="history-icon"><Clock /></el-icon>
              <h3 class="card-title">历史设备</h3>
            </div>
            <div class="card-actions" v-show="deviceStore.pairedDevices.length > 0">
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                circle
                @click="clearAllPairedDevices"
                title="清除所有历史设备"
              />
            </div>

          </div>
          
          <div class="device-list">
            <div 
              v-for="device in deviceStore.pairedDevices" 
              :key="device.id"
              class="device-item"
              :class="{ 'highlighted': device.id === 'iphone16' }"
              @click="connectToDevice(device)"
            >
              <div class="device-info">
                <div class="device-icon-container">
                  <el-icon size="20" class="device-icon">
                    <Monitor v-if="device.type === 'desktop'" />
                    <Iphone v-else />
                  </el-icon>
                  <div class="device-status-indicator" :class="{ 'online': device.isOnline, 'offline': !device.isOnline }"></div>
                </div>
                <div class="device-details">
                  <div class="device-name">{{ getDeviceDisplayName(device) }}</div>
                  <div class="device-meta">
                    <span class="last-connected">{{ formatLastConnected(device.lastSeen) }}</span>
                  </div>
                </div>
              </div>
              <div class="device-actions">
                <button 
                  class="reconnect-button"
                  :disabled="connectingDevices.has(device.id)"
                >
                  {{ connectingDevices.has(device.id) ? '连接中' : '重连' }}
                </button>
                <button 
                  class="remove-button"
                  @click.stop="removePairedDevice(device)"
                >
                  删除
                </button>
              </div>
            </div>
            
            <!-- 历史设备空状态 -->
            <div v-if="deviceStore.pairedDevices.length === 0" class="empty-state history-empty">
              <el-icon class="empty-icon"><DocumentRemove /></el-icon>
              <p class="empty-text">还未连接任何设备</p>
              <p class="empty-tip">成功连接设备后，历史记录将显示在这里</p>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer区域 - 根据设计方案布局 -->
    <footer class="page-footer">
      <div class="footer-content">
        <span class="copyright">copyright © 2025</span>
        <span class="registration">备案号</span>
        <div class="footer-icons">
          <a href="https://github.com" class="footer-icon" title="GitHub">
            <el-icon><Link /></el-icon>
          </a>
          <a href="#" class="footer-icon" title="关于我们">
            <el-icon><QuestionFilled /></el-icon>
          </a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { 
  Monitor, Iphone, Connection, Search, Link, Delete, 
  Refresh, Sunny, Moon, FolderOpened, QuestionFilled, Clock, DocumentRemove 
} from '@element-plus/icons-vue'
import QRCode from 'qrcode'
import { useDeviceStore, useWebSocketStore, useAppStore, useMessageStore } from '../store'
import { getDeviceInfo, detectNetworkMode, getEnhancedDeviceInfo, getEnhancedDeviceInfoSync } from '../utils/device'

// Store
const deviceStore = useDeviceStore()
const wsStore = useWebSocketStore()
const appStore = useAppStore()
const messageStore = useMessageStore()
const router = useRouter()

// 响应式数据
const connectionMode = ref('qr') // 'qr' 或 'code'
const inputPairingCode = ref('')
const qrcodeElement = ref(null)
const refreshing = ref(false)
const connecting = ref(false)
const connectingDevices = ref(new Set())

// 计算属性
const currentOrigin = computed(() => window.location.origin)

// 生成二维码
const generateQRCode = async () => {
  if (!qrcodeElement.value || !deviceStore.pairingCode) {
    return
  }

  try {
    qrcodeElement.value.innerHTML = ''

    const connectionInfo = {
      type: 'wsshare_connection',
      code: deviceStore.pairingCode,
      device: deviceStore.currentDevice.name,
      ip: deviceStore.currentDevice.ip,
      url: currentOrigin.value,
      version: '1.0'
    }

    const qrDataURL = await QRCode.toDataURL(JSON.stringify(connectionInfo), {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 200
    })

    qrcodeElement.value.innerHTML = `<img src="${qrDataURL}" alt="连接二维码" class="qr-image" />`
  } catch (error) {
    qrcodeElement.value.innerHTML = `
      <div class="qr-error">
        <el-icon><Close /></el-icon>
        <span>二维码生成失败</span>
      </div>
    `
  }
}

// 刷新配对码
const refreshPairingCode = async () => {
  refreshing.value = true
  
  try {
    // 重新连接WebSocket获取新的配对码
    wsStore.disconnect()
    await initWebSocket()
    
    ElMessage.success('配对码已刷新')
  } catch (error) {
    ElMessage.error('刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}

// 通过配对码连接
const connectByCode = async () => {
  if (inputPairingCode.value.length !== 4) {
    ElMessage.warning('请输入4位配对码')
    return
  }
  
  connecting.value = true
  
  try {
    const success = wsStore.send({
      type: 'connect_by_code',
      data: {
        code: inputPairingCode.value
      }
    })
    
    if (!success) {
      throw new Error('WebSocket连接未建立')
    }
    
    // 显示连接中状态
    ElLoading.service({
      lock: true,
      text: '正在连接...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
  } catch (error) {
    ElMessage.error('连接失败：' + error.message)
  } finally {
    connecting.value = false
  }
}

// 发现设备
const discoverDevices = () => {
  deviceStore.discovering = true
  
  wsStore.send({
    type: 'discover_devices',
    data: {}
  })
  
  setTimeout(() => {
    deviceStore.discovering = false
  }, 2000)
}

// 连接到设备
const connectToDevice = async (device) => {
  connectingDevices.value.add(device.id)
  
  try {
    const success = wsStore.send({
      type: 'connect_request',
      data: {
        target_device: device.id,
        source_device: deviceStore.currentDevice
      }
    })
    
    if (!success) {
      throw new Error('WebSocket连接未建立')
    }
    
    ElLoading.service({
      lock: true,
      text: `正在连接到 ${device.name}...`,
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
  } catch (error) {
    ElMessage.error('连接失败：' + error.message)
    connectingDevices.value.delete(device.id)
  }
}

// 移除已配对设备
const removePairedDevice = async (device) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除设备 "${device.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    deviceStore.removePairedDevice(device.id)
    ElMessage.success('设备已移除')
  } catch {
    // 用户取消删除
  }
}

// 清除所有已配对设备
const clearAllPairedDevices = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清除所有历史设备吗？这将删除 ${deviceStore.pairedDevices.length} 个设备的记录。`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 清除所有已配对设备
    deviceStore.pairedDevices.length = 0
    deviceStore.savePairedDevices()
    ElMessage.success('所有历史设备已清除')
  } catch {
    // 用户取消清除
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '未知'
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 格式化最后连接时间 - 精确到年月日时分
const formatLastConnected = (timestamp) => {
  if (!timestamp) return '未知时间'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}年${month}月${day}日 ${hours}:${minutes}`
}

// 获取设备信息的包装函数 - 兼容原有调用
const getDeviceInfoForWebSocket = async () => {
  try {
    return await getEnhancedDeviceInfo()
  } catch (error) {
    // 返回默认值
    return {
      deviceName: '未知设备',
      systemName: '未知系统',
      systemVersion: '',
      deviceType: 'desktop',
      manufacturer: ''
    }
  }
}

// 这些函数已经被新的设备检测器替代，保留用于向后兼容

// 兼容原有的设备显示名称函数
const getDeviceDisplayName = (device) => {
  if (!device) return '未知设备'

  // 如果设备对象有详细信息，直接使用
  if (device.model_name) return device.model_name
  if (device.deviceName) return device.deviceName
  if (device.name) return device.name

  // 使用同步版本的设备检测器
  const deviceInfo = getEnhancedDeviceInfoSync()
  return deviceInfo.deviceName
}

// 获取系统信息 - 使用增强的检测逻辑
const getSystemInfo = () => {
  const userAgent = navigator.userAgent

  // Android 检测 - 优先检测，避免被 Linux 误判
  // 增强Android检测，包括Samsung设备的特殊情况
  if (/Android/.test(userAgent) ||
      (/Samsung|SM-/.test(userAgent) && (/Mobile|wv|AppleWebKit/.test(userAgent))) ||
      /SM-[A-Z]\d+/.test(userAgent) ||
      (/Samsung/.test(userAgent) && /Linux/.test(userAgent) && /Mobile/.test(userAgent))) {
    const match = userAgent.match(/Android (\d+(?:\.\d+)?)/)
    if (match) {
      return `Android ${match[1]}`
    }
    return 'Android'
  }

  // iOS 版本检测
  if (userAgent.includes('iPhone OS') || userAgent.includes('iOS')) {
    const match = userAgent.match(/OS (\d+)_(\d+)/)
    if (match) {
      return `iOS ${match[1]}.${match[2]}`
    }
    return 'iOS'
  }

  // Windows 版本检测
  if (userAgent.includes('Windows NT 10.0')) return 'Windows 11'
  if (userAgent.includes('Windows NT 6.3')) return 'Windows 8.1'
  if (userAgent.includes('Windows NT 6.2')) return 'Windows 8'
  if (userAgent.includes('Windows NT 6.1')) return 'Windows 7'
  if (userAgent.includes('Windows')) return 'Windows'

  // macOS 版本检测
  if (userAgent.includes('Mac OS X')) {
    const match = userAgent.match(/Mac OS X (\d+)_(\d+)/)
    if (match) {
      const major = parseInt(match[1])
      const minor = parseInt(match[2])
      if (major >= 10 && minor >= 15) return 'macOS Catalina+'
      if (major >= 10 && minor >= 14) return 'macOS Mojave'
      if (major >= 10 && minor >= 13) return 'macOS High Sierra'
    }
    return 'macOS'
  }

  // Linux 检测 - 放在最后，避免误判Android设备
  if (userAgent.includes('Linux') && !(/Android|Samsung|SM-/.test(userAgent))) {
    return 'Linux'
  }

  return '未知系统'
}

// WebSocket初始化
const initWebSocket = async () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  let wsHost = window.location.hostname
  if (wsHost === 'localhost' || wsHost === '127.0.0.1') {
    wsHost = 'localhost'
  }
  const wsUrl = `${protocol}//${wsHost}:8080/api/ws`
  
  try {
    await wsStore.connect(wsUrl)
    
    // 设置消息处理器
    wsStore.onMessage(handleWebSocketMessage)
    
    // 使用store中已获取的设备信息（包含真实主机名）
    const enhancedDeviceInfo = getEnhancedDeviceInfoSync()
    
    // 发送设备注册信息，优先使用store中的设备名称
    wsStore.send({
      type: 'device_info',
      data: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        deviceName: deviceStore.currentDevice?.name || enhancedDeviceInfo.deviceName,  // 优先使用store中的名称
        deviceType: deviceStore.currentDevice?.type || enhancedDeviceInfo.deviceType,
        systemName: enhancedDeviceInfo.systemName,
        systemVersion: enhancedDeviceInfo.systemVersion,
        manufacturer: enhancedDeviceInfo.manufacturer,
        
        // 屏幕和系统信息 - 使用增强的设备信息
        screenWidth: enhancedDeviceInfo.screenWidth,
        screenHeight: enhancedDeviceInfo.screenHeight,
        pixelRatio: enhancedDeviceInfo.pixelRatio,
        language: enhancedDeviceInfo.language,
        languages: enhancedDeviceInfo.languages,
        timezone: enhancedDeviceInfo.timezone,
        colorDepth: enhancedDeviceInfo.colorDepth,
        touchSupport: enhancedDeviceInfo.touchSupport,
        maxTouchPoints: enhancedDeviceInfo.maxTouchPoints,
        vendor: navigator.vendor,
        appVersion: navigator.appVersion
      }
    })
    
  } catch (error) {
    ElMessage.error('网络连接失败，请检查网络设置')
  }
}

// WebSocket消息处理
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'device_registered':
      if (message.data.pairing_code) {
        deviceStore.pairingCode = message.data.pairing_code
        if (message.data.device_name) {
          deviceStore.updateCurrentDevice({
            ...deviceStore.currentDevice,
            name: message.data.device_name,
            type: message.data.device_type || deviceStore.currentDevice.type
          })
        }
        generateQRCode()
      }
      break
      
    case 'device_list':
      deviceStore.availableDevices.length = 0
      message.data?.forEach(device => {
        deviceStore.addAvailableDevice(device)
      })
      break
      
    case 'connection_established':
      
      ElLoading.service().close()
      connectingDevices.value.clear()
      inputPairingCode.value = ''
      
      // 添加到已连接设备
      deviceStore.addConnectedDevice(message.data)
      
      // 设置活动设备
      messageStore.setActiveDevice(message.data.id)
      
      ElMessage.success(`已连接到 ${message.data.name}`)
      
      // 短暂延迟跳转，确保消息处理完成
      setTimeout(() => {
        router.push(`/transfer/${message.data.id}`)
      }, 500)
      break
      
    case 'connection_failed':
      ElLoading.service().close()
      connectingDevices.value.clear()
      ElMessage.error(`连接失败: ${message.data.reason || '未知错误'}`)
      break
      
    case 'error':
      ElLoading.service().close()
      connectingDevices.value.clear()
      ElMessage.error(message.data.message || '发生错误')
      break
  }
}

// 重新注册设备信息（用于重连后）
const reRegisterDevice = () => {
  
  // 使用同步版本的设备检测，避免HTTP请求干扰WebSocket连接
  const enhancedDeviceInfo = getEnhancedDeviceInfoSync()
  
  // 发送设备注册信息，优先使用store中的设备名称
  wsStore.send({
    type: 'device_info',
    data: {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      deviceName: deviceStore.currentDevice?.name || enhancedDeviceInfo.deviceName,  // 优先使用store中的名称
      deviceType: deviceStore.currentDevice?.type || enhancedDeviceInfo.deviceType,
      systemName: enhancedDeviceInfo.systemName,
      systemVersion: enhancedDeviceInfo.systemVersion,
      manufacturer: enhancedDeviceInfo.manufacturer,
      
      // 屏幕和系统信息 - 使用增强的设备信息
      screenWidth: enhancedDeviceInfo.screenWidth,
      screenHeight: enhancedDeviceInfo.screenHeight,
      pixelRatio: enhancedDeviceInfo.pixelRatio,
      language: enhancedDeviceInfo.language,
      languages: enhancedDeviceInfo.languages,
      timezone: enhancedDeviceInfo.timezone,
      colorDepth: enhancedDeviceInfo.colorDepth,
      touchSupport: enhancedDeviceInfo.touchSupport,
      maxTouchPoints: enhancedDeviceInfo.maxTouchPoints,
      vendor: navigator.vendor,
      appVersion: navigator.appVersion
    }
  })
}

// 响应式处理
const isMobile = ref(window.innerWidth <= 768)

const handleResize = () => {
  const newIsMobile = window.innerWidth <= 768
  if (isMobile.value !== newIsMobile) {
    isMobile.value = newIsMobile

    // 强制重新计算样式
    nextTick(() => {
      // 触发重新渲染
      document.documentElement.style.setProperty('--force-reflow', Math.random().toString())

      // 强制重新计算布局
      document.body.offsetHeight
    })
  }
}

// 处理设备方向变化（特别针对iPad）
const handleOrientationChange = () => {
  // 延迟执行，等待方向变化完成
  setTimeout(() => {
    handleResize()
  }, 100)
}

// 生命周期
onMounted(async () => {
  // 初始化主题
  appStore.initTheme()

  // 加载已配对设备
  deviceStore.loadPairedDevices()

  // 获取设备信息
  await getDeviceInfo(deviceStore)

  // 检测网络模式
  await detectNetworkMode(deviceStore)

  // 设置全局重新注册设备函数
  window.reRegisterDevice = reRegisterDevice

  // 初始化WebSocket
  await initWebSocket()

  // 发现设备
  setTimeout(() => {
    discoverDevices()
  }, 1000)

  // 添加窗口大小监听
  window.addEventListener('resize', handleResize)
  // 添加设备方向变化监听（特别针对iPad）
  window.addEventListener('orientationchange', handleOrientationChange)
})

onUnmounted(() => {
  // 移除窗口大小监听
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('orientationchange', handleOrientationChange)

  // 暂时不自动断开WebSocket，让其他页面可以复用连接
})
</script>

<style scoped>
@import '../assets/styles/home-new.css';
</style> 