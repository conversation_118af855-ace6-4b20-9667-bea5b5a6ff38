import { ref, computed, nextTick } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import QRCode from 'qrcode'
import { useDeviceStore, useWebSocketStore } from '../store'
import { getEnhancedDeviceInfoSync } from '../utils/device'

export function useDevicePairing() {
  const deviceStore = useDeviceStore()
  const wsStore = useWebSocketStore()
  
  // 响应式数据
  const inputPairingCode = ref('')
  const refreshing = ref(false)
  const connecting = ref(false)
  const connectingDevices = ref(new Set())
  
  // 计算属性
  const currentOrigin = computed(() => window.location.origin)
  const currentDevice = computed(() => deviceStore.currentDevice)
  const pairingCode = computed(() => deviceStore.pairingCode)
  
  // 生成二维码 - 直接复用HomePage逻辑
  const generateQRCode = async (qrcodeElement) => {
    if (!qrcodeElement || !pairingCode.value) {
      return
    }

    try {
      qrcodeElement.innerHTML = ''

      const connectionInfo = {
        type: 'wsshare_connection',
        code: pairingCode.value,
        device: currentDevice.value?.name || '当前设备',
        ip: currentDevice.value?.ip,
        url: currentOrigin.value,
        version: '1.0'
      }

      const qrDataURL = await QRCode.toDataURL(JSON.stringify(connectionInfo), {
        errorCorrectionLevel: 'M',
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      })

      qrcodeElement.innerHTML = `<img src="${qrDataURL}" alt="连接二维码" class="qr-image" />`
    } catch (error) {
      qrcodeElement.innerHTML = `
        <div class="qr-error">
          <el-icon><Close /></el-icon>
          <span>二维码生成失败</span>
        </div>
      `
    }
  }

  // 刷新配对码 - 直接复用HomePage逻辑
  const refreshPairingCode = async () => {
    refreshing.value = true
    
    try {
      // 重新连接WebSocket获取新的配对码
      wsStore.disconnect()
      await initWebSocket()
      
      ElMessage.success('配对码已刷新')
    } catch (error) {
      ElMessage.error('刷新失败，请重试')
    } finally {
      refreshing.value = false
    }
  }

  // 通过配对码连接 - 直接复用HomePage逻辑
  const connectByCode = async () => {
    if (inputPairingCode.value.length !== 4) {
      ElMessage.warning('请输入4位配对码')
      return
    }
    
    connecting.value = true
    
    try {
      const success = wsStore.send({
        type: 'connect_by_code',
        data: {
          code: inputPairingCode.value
        }
      })
      
      if (!success) {
        throw new Error('WebSocket连接未建立')
      }
      
      // 显示连接中状态
      ElLoading.service({
        lock: true,
        text: '正在连接...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
    } catch (error) {
      ElMessage.error('连接失败：' + error.message)
    } finally {
      connecting.value = false
    }
  }

  // 连接到设备 - 直接复用HomePage逻辑
  const connectToDevice = async (device) => {
    connectingDevices.value.add(device.id)
    
    try {
      const success = wsStore.send({
        type: 'connect_request',
        data: {
          target_device: device.id,
          source_device: currentDevice.value
        }
      })
      
      if (!success) {
        throw new Error('WebSocket连接未建立')
      }
      
      ElLoading.service({
        lock: true,
        text: `正在连接到 ${device.name}...`,
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
    } catch (error) {
      ElMessage.error('连接失败：' + error.message)
      connectingDevices.value.delete(device.id)
    }
  }

  // WebSocket初始化 - 简化版本
  const initWebSocket = async () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    let wsHost = window.location.hostname
    if (wsHost === 'localhost' || wsHost === '127.0.0.1') {
      wsHost = 'localhost'
    }
    const wsUrl = `${protocol}//${wsHost}:8080/api/ws`
    
    try {
      await wsStore.connect(wsUrl)
      
      // 使用store中已获取的设备信息
      const enhancedDeviceInfo = getEnhancedDeviceInfoSync()
      
      // 发送设备注册信息
      wsStore.send({
        type: 'device_info',
        data: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          deviceName: currentDevice.value?.name || enhancedDeviceInfo.deviceName,
          deviceType: currentDevice.value?.type || enhancedDeviceInfo.deviceType,
          systemName: enhancedDeviceInfo.systemName,
          systemVersion: enhancedDeviceInfo.systemVersion,
          manufacturer: enhancedDeviceInfo.manufacturer,
          
          // 屏幕和系统信息
          screenWidth: enhancedDeviceInfo.screenWidth,
          screenHeight: enhancedDeviceInfo.screenHeight,
          pixelRatio: enhancedDeviceInfo.pixelRatio,
          language: enhancedDeviceInfo.language,
          languages: enhancedDeviceInfo.languages,
          timezone: enhancedDeviceInfo.timezone,
          colorDepth: enhancedDeviceInfo.colorDepth,
          touchSupport: enhancedDeviceInfo.touchSupport,
          maxTouchPoints: enhancedDeviceInfo.maxTouchPoints,
          vendor: navigator.vendor,
          appVersion: navigator.appVersion
        }
      })
      
    } catch (error) {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
  }

  // WebSocket消息处理 - 复用HomePage逻辑
  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'device_registered':
        if (message.data.pairing_code) {
          deviceStore.pairingCode = message.data.pairing_code
          if (message.data.device_name) {
            deviceStore.updateCurrentDevice({
              ...deviceStore.currentDevice,
              name: message.data.device_name,
              type: message.data.device_type || deviceStore.currentDevice.type
            })
          }
        }
        break
        
      case 'connection_established':
        ElLoading.service().close()
        connectingDevices.value.clear()
        inputPairingCode.value = ''
        
        // 添加到已连接设备
        deviceStore.addConnectedDevice(message.data)
        
        ElMessage.success(`已连接到 ${message.data.name}`)
        
        return message.data // 返回连接的设备信息，供调用者处理路由跳转
        
      case 'connection_failed':
        ElLoading.service().close()
        connectingDevices.value.clear()
        ElMessage.error(`连接失败: ${message.data.reason || '未知错误'}`)
        break
        
      case 'error':
        ElLoading.service().close()
        connectingDevices.value.clear()
        ElMessage.error(message.data.message || '发生错误')
        break
    }
    
    return null
  }

  // 复制配对码
  const copyPairingCode = async () => {
    if (!pairingCode.value) return
    
    try {
      await navigator.clipboard.writeText(pairingCode.value)
      ElMessage.success('配对码已复制到剪贴板')
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = pairingCode.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('配对码已复制到剪贴板')
    }
  }

  return {
    // 响应式数据
    inputPairingCode,
    refreshing,
    connecting,
    connectingDevices,
    
    // 计算属性
    currentDevice,
    pairingCode,
    
    // 方法
    generateQRCode,
    refreshPairingCode,
    connectByCode,
    connectToDevice,
    handleWebSocketMessage,
    copyPairingCode,
    initWebSocket
  }
}
