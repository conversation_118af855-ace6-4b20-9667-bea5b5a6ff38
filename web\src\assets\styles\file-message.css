/* ========================================
   文件消息组件样式 - 微信风格气泡
   ======================================== */

/* 聊天消息容器 */
.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden; /* 防止水平溢出 */
  height: 100%;
  width: 100%; /* 确保容器宽度 */
  box-sizing: border-box;
}

/* ========================================
   消息项基础样式
   ======================================== */

/* 消息项容器 */
.message-item {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  margin-bottom: 8px;
  animation: messageSlideIn 0.3s ease-out;
  min-width: 0; /* 允许flex项目缩小 */
  overflow: hidden; /* 防止内容溢出 */
  box-sizing: border-box;
}

/* 本地消息（发送的消息，右侧对齐） */
.message-local {
  align-self: flex-end;
  align-items: flex-end;
}

/* 远程消息（接收的消息，左侧对齐） */
.message-remote {
  align-self: flex-start;
  align-items: flex-start;
}

/* ========================================
   文本消息气泡样式
   ======================================== */

/* 文本消息容器 - 使用气泡样式 */
.text-message {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 文本消息气泡 - 使用和文件消息相同的气泡样式 */
.text-message .message-content {
  /* 使用气泡基础样式 */
  max-width: min(75%, 480px);
  min-width: 80px; /* 👈 调整文本气泡最小宽度，适应短文本 */
  border-radius: 12px !important; /* 统一的圆角，和文件气泡一样 */
  position: relative !important;
  box-sizing: border-box !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
  z-index: 2 !important; /* 气泡主体在上层 */

  /* 文本内容样式 */
  padding: 16px !important; /* 和文件气泡一样的内边距 */
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  white-space: pre-wrap !important;
  line-height: 1.4 !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
  overflow: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 本地文本消息气泡 - 青绿色背景 */
.message-local .text-message .message-content {
  background: #14a19b !important; /* 主题青绿色 */
  color: white !important;
  border-color: rgba(20, 161, 155, 0.2) !important;
  box-shadow: 0 1px 4px rgba(20, 161, 155, 0.2) !important;
}

/* 远程文本消息气泡 - 灰色背景 */
.message-remote .text-message .message-content {
  background: #a9a6a6 !important; /* 和文件气泡一样的灰色 */
  color: #333 !important;
  border-color: #e0e0e0 !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
}

/* 消息时间戳 */
.message-time {
  font-size: 11px;
  color: var(--text-secondary);
  opacity: 0.7;
  margin-top: 4px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 消息状态指示器 */
.message-status {
  display: inline-flex;
  align-items: center;
  font-size: 10px;
  opacity: 0.6;
}

.message-status.sending {
  color: #f59e0b;
}

.message-status.sent {
  color: #10b981;
}

.message-status.failed {
  color: #ef4444;
}

/* 状态图标 */
.message-status::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
  background: currentColor;
}

/* ========================================
   文件消息气泡样式
   ======================================== */

/* 文件消息容器 */
.file-message {
  margin-bottom: 20px; /* 增加间距，为带边框的指向标留出空间 */
  display: flex;
  align-items: flex-start;
  gap: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.file-message.is-local {
  flex-direction: row-reverse;
}

.file-message.is-remote {
  flex-direction: row;
}

/* 设备图标样式 */
.device-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(20, 161, 155, 0.1);
  border-radius: 50%;
  margin-top: 4px;
  border: 2px solid rgba(20, 161, 155, 0.2);
}

.is-remote .device-icon {
  background: rgba(144, 147, 153, 0.1);
  border-color: rgba(144, 147, 153, 0.2);
}

/* ========================================
   微信风格气泡基础样式 - 统一设计
   ======================================== */

/* 气泡基础样式 - 微信风格 */
.message-bubble {
  max-width: min(75%, 480px);
  min-width: 200px; /* 👈 调整文件气泡最小宽度，更合理 */
  border-radius: 12px; /* 统一的圆角 */
  position: relative;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  z-index: 2; /* 气泡主体在上层 */
}

/* 文件气泡 - 灰色背景 */
.message-bubble.file-bubble,
.file-message .message-bubble {
  background: #dae8fe !important; /* 移除透明度，使用纯色 */
  color: #333 !important;
  padding: 16px;
  border: 1px solid d4e4fc;
}

/* 文字气泡 - 根据消息方向设置不同颜色 */
.message-bubble.text-bubble {
  padding: 16px !important;
  min-width: 80px !important; /* 👈 调整文字气泡最小宽度，适应短文本 */
}

/* 本地文字气泡 - 青绿色背景 */
.file-message.is-local .message-bubble.text-bubble {
  background: #14a19b !important;
  color: white !important;
  border-color: rgba(20, 161, 155, 0.2) !important;
}

/* 远程文字气泡 - 灰色背景 */
.file-message.is-remote .message-bubble.text-bubble {
  background: #dae8fe  !important;
  color: #333 !important;
  border-color: #e0e0e0 !important;
}

/* 文本内容样式 */
.text-content {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  font-size: 14px;
}

/* ========================================
   微信风格指向标 - 带边框效果
   ======================================== */

/* 微信风格的小尖角效果 - 完全融合 */
.message-bubble::after,
.text-message .message-content::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  top: 16px; /* 稍微下移，更居中 */
  z-index: 1; /* 指向标在下层 */
}

/* 隐藏边框效果，实现完全融合 */
.message-bubble::before,
.text-message .message-content::before {
  display: none; /* 完全隐藏边框，消除分离感 */
}

/* 本地消息 - 右上角小尖角 - 完全融合 */
.is-local .message-bubble::after,
.message-local .message-bubble::after,
.message-local .text-message .message-content::after {
  right: -8px; /* 减少突出距离，更贴合气泡 */
  border-width: 8px 0 8px 10px; /* 稍微缩小指向标，更精致 */
  border-color: transparent;
}

/* 本地文件气泡指向标颜色 - 完全融合 */
.is-local .message-bubble.file-bubble::after,
.message-local .message-bubble::after,
.file-message.is-local .message-bubble::after {
  border-left-color: #dae8fe !important; /* 与气泡背景色完全一致 */
}

/* 本地文字气泡指向标颜色 - 完全融合 */
.is-local .message-bubble.text-bubble::after,
.message-local .text-message .message-content::after,
.file-message.is-local .message-bubble.text-bubble::after {
  border-left-color: #14a19b !important;
}

/* 远程消息 - 左上角小尖角 - 完全融合 */
.is-remote .message-bubble::after,
.message-remote .message-bubble::after,
.message-remote .text-message .message-content::after {
  left: -6px; /* 减少突出距离，更贴合气泡 */
  border-width: 8px 8px 10px 0; /* 稍微缩小指向标，更精致 */
  border-color: transparent;
}

/* 远程文件气泡指向标颜色 - 完全融合 */
.is-remote .message-bubble.file-bubble::after,
.message-remote .message-bubble::after,
.file-message.is-remote .message-bubble::after {
  border-right-color: #dae8fe  !important; /* 与气泡背景色完全一致 */
}

/* 远程文字气泡指向标颜色 - 完全融合 */
.is-remote .message-bubble.text-bubble::after,
.message-remote .text-message .message-content::after,
.file-message.is-remote .message-bubble.text-bubble::after {
  border-right-color: #dae8fe  !important; /* 远程文字消息使用灰色 */
}

/* 文件内容布局 */
.file-content {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 16px;
  align-items: center;
  width: 100%;
}

/* 文件图标容器 */
.file-icon {
  flex-shrink: 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 远程消息的文件图标 */
.file-message.is-remote .file-icon {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 文件信息区域 */
.file-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 文件名 */
.file-name {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

/* 文件元信息 */
.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  opacity: 0.8;
}

/* 文件大小 */
.file-size {
  font-weight: 400;
}

/* 文件时间 */
.file-time {
  font-weight: 400;
}

/* 文件操作按钮区域 */
.file-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

/* ========================================
   深色主题适配
   ======================================== */

/* 暗色主题 - 本地文字气泡 */
.dark-theme .file-message.is-local .message-bubble.text-bubble {
  background: #213b40 !important;  /* 👈 暗色主题本地文字气泡背景色 */
  color: #f8fafaff !important;       /* 👈 暗色主题本地文字气泡文字色 */
  border-color: #047e2f !important;
}

/* 暗色主题 - 远程文字气泡 */
.dark-theme .file-message.is-remote .message-bubble.text-bubble {
  background: #353d48 !important;  /* 👈 暗色主题远程文字气泡背景色 */
  color: #f6f8f8ff !important;       /* 👈 暗色主题远程文字气泡文字色 */
  border-color: #38a8ed !important;
}

/* 暗色主题 - 文件气泡 */
.dark-theme .message-bubble.file-bubble,
.dark-theme .file-message .message-bubble {
  background: #353d48 !important;  /* 👈 暗色主题文件气泡背景色 */
  color: #f6f8f8ff!important;       /* 👈 暗色主题文件气泡文字色 */
  border-color: #38a8ed !important;
}
/* 暗色主题指向标颜色 */
.dark-theme .file-message.is-local .message-bubble.text-bubble::after {
  border-left-color: #213b40!important;  /* 👈 暗色主题本地文字气泡指向标颜色 */
}

.dark-theme .file-message.is-remote .message-bubble.text-bubble::after {
  border-right-color: #353d48  !important; /* 👈 暗色主题远程文字气泡指向标颜色 */
}

/* 暗色主题 - 文件气泡指向标颜色 */
.dark-theme .file-message.is-local .message-bubble.file-bubble::after,
.dark-theme .is-local .message-bubble.file-bubble::after {
  border-left-color: #353d48 !important;  /* 👈 暗色主题本地文件气泡指向标颜色 */
}

.dark-theme .file-message.is-remote .message-bubble.file-bubble::after,
.dark-theme .is-remote .message-bubble.file-bubble::after {
  border-right-color: #353d48 !important; /* 👈 暗色主题远程文件气泡指向标颜色 */
}

/* 暗色主题 - 文件文字颜色设置 */
.dark-theme .file-name {
  color: #e2e8f0 !important; /* 👈 暗色主题文件名颜色 - 明亮的灰白色 */
}

.dark-theme .file-meta {
  color: #94a3b8 !important; /* 👈 暗色主题文件元信息颜色 - 中等灰色 */
}

.dark-theme .file-size {
  color: #64748b !important; /* 👈 暗色主题文件大小颜色 - 较暗的灰色 */
}

.dark-theme .file-time {
  color: #64748b !important; /* 👈 暗色主题文件时间颜色 - 较暗的灰色 */
}
/* ========================================
   消息选择和交互状态
   ======================================== */

/* 消息选中状态 */
.message-item.selected .message-content,
.file-message.selected .message-bubble {
  outline: 2px solid #14a19b;
  outline-offset: 2px;
}

.message-item.selected.message-local .message-content,
.file-message.selected.is-local .message-bubble {
  background: rgba(20, 161, 155, 0.1) !important;
}

.message-item.selected.message-remote .message-content,
.file-message.selected.is-remote .message-bubble {
  background: rgba(0, 144, 255, 0.1) !important;
}

/* 消息长按状态 */
.message-item.long-pressing .message-content,
.file-message.long-pressing .message-bubble {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 消息加载状态 */
.message-item.loading .message-content,
.file-message.loading .message-bubble {
  opacity: 0.6;
  position: relative;
}

.message-item.loading .message-content::after,
.file-message.loading .message-bubble::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: messageLoading 1s linear infinite;
}

@keyframes messageLoading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   动画效果
   ======================================== */

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateY(5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 气泡悬停效果 */
.message-content:hover {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-local .message-content:hover {
  box-shadow: 0 2px 8px rgba(20, 161, 155, 0.25);
}

.message-remote .message-content:hover {
  box-shadow: 0 2px 8px rgba(0, 144, 255, 0.25);
}

.file-message .message-bubble:hover {
  transform: translateY(-1px) scale(1.005);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-message.is-local .message-bubble:hover {
  box-shadow: 0 3px 12px rgba(20, 161, 155, 0.2);
}

.file-message.is-remote .message-bubble:hover {
  box-shadow: 0 3px 12px rgba(0, 144, 255, 0.2);
}

/* 深色主题悬停效果 */
.dark-theme .message-remote .message-content:hover {
  box-shadow: 0 2px 8px rgba(0, 144, 255, 0.3);
}

.dark-theme .file-message.is-local .message-bubble:hover {
  box-shadow: 0 3px 12px rgba(20, 161, 155, 0.25);
}

.dark-theme .file-message.is-remote .message-bubble:hover {
  box-shadow: 0 3px 12px rgba(0, 144, 255, 0.25);
}

/* ========================================
   响应式设计 - 移动端适配
   ======================================== */

@media (max-width: 768px) {
  /* 移动端聊天容器 */
  .chat-messages {
    padding: 12px;
    gap: 10px;
    overflow-x: hidden !important; /* 强制防止水平溢出 */
    width: 100% !important;
  }

  /* 移动端消息项 */
  .message-item {
    max-width: 85% !important;
    margin-bottom: 6px;
    min-width: 0 !important; /* 允许缩小 */
    overflow: hidden !important; /* 防止溢出 */
  }

  /* 移动端文本消息 */
  .message-content {
    padding: 10px 14px;
    font-size: 15px; /* 移动端稍大的字体 */
    line-height: 1.5;
    border-radius: 16px;
    word-break: break-word !important; /* 强制换行 */
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  /* 移动端文件消息气泡 */
  .file-message .message-bubble {
    min-width: 180px; /* 👈 调整移动端文件气泡最小宽度 */
    max-width: min(85%, 400px);
    padding: 14px;
    border-radius: 16px;
  }

  /* 移动端文件内容 */
  .file-content {
    gap: 12px;
  }

  /* 移动端文件图标 */
  .file-icon {
    padding: 8px;
    border-radius: 10px;
  }

  /* 移动端文件信息 */
  .file-name {
    font-size: 15px;
  }

  .file-meta {
    font-size: 13px;
  }

  /* 移动端文件操作按钮 */
  .file-actions .el-button {
    padding: 8px 16px;
    font-size: 13px;
    min-height: 36px; /* 确保触摸友好 */
  }

  /* 移动端时间戳 */
  .message-time {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  /* 小屏幕聊天容器 */
  .chat-messages {
    padding: 8px;
    gap: 8px;
    overflow-x: hidden !important; /* 强制防止水平溢出 */
    width: 100% !important;
  }

  /* 小屏幕消息项 */
  .message-item {
    max-width: 92% !important;
    margin-bottom: 4px;
    min-width: 0 !important;
    overflow: hidden !important;
  }

  /* 小屏幕文本消息 */
  .message-content {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 14px;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  /* 小屏幕文件消息气泡 */
  .file-message .message-bubble {
    min-width: 140px; /* 👈 调整小屏幕文件气泡最小宽度 */
    max-width: min(92%, 300px);
    padding: 12px;
    border-radius: 14px;
  }

  /* 小屏幕文件内容 */
  .file-content {
    gap: 10px;
  }

  /* 小屏幕文件图标 */
  .file-icon {
    padding: 6px;
    border-radius: 8px;
  }

  /* 小屏幕文件名 */
  .file-name {
    font-size: 14px;
  }

  /* 小屏幕文件元信息 */
  .file-meta {
    font-size: 12px;
  }

  /* 小屏幕文件操作按钮 */
  .file-actions {
    gap: 6px;
    flex-wrap: wrap;
  }

  .file-actions .el-button {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
    flex: 1;
    min-width: 60px;
  }

  /* 小屏幕时间戳 */
  .message-time {
    font-size: 11px;
  }

  /* 小屏幕禁用悬停效果 */
  .message-content:hover,
  .file-message .message-bubble:hover {
    transform: none;
    transition: none;
  }
}

/* ========================================
   文件消息详细样式补充
   ======================================== */

/* 文件图标区域 */
.file-icon-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-type-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 文件信息区域 */
.file-info-section {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.file-name {
  font-weight: 600;
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  max-height: 2.8em;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

.file-size {
  color: #888;
  font-weight: 500;
}

/* 操作按钮区域 */
.file-actions-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

/* 圆形按钮通用样式 */
.download-btn,
.delete-btn {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
  box-sizing: border-box !important;
}

/* 下载按钮颜色 */
.download-btn {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: white !important;
}

.download-btn:hover {
  background: #66b1ff !important;
  border-color: #66b1ff !important;
  transform: scale(1.1) !important;
}

/* 删除按钮颜色 */
.delete-btn {
  background: #F56C6C !important;
  border-color: #F56C6C !important;
  color: white !important;
}

.delete-btn:hover {
  background: #f78989 !important;
  border-color: #f78989 !important;
  transform: scale(1.1) !important;
}

/* 移动端按钮样式 */
@media (max-width: 768px) {
  .download-btn,
  .delete-btn {
    width: 28px !important;
    height: 28px !important;
  }

  .file-actions-section {
    min-width: 32px;
  }
}

@media (max-width: 480px) {
  .file-actions-section {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: center;
    gap: 12px;
    margin-top: 8px;
  }

  .download-btn,
  .delete-btn {
    width: 28px !important;
    height: 28px !important;
    flex: none !important;
  }
}
