/**
 * 文件传输增强功能组合式函数
 * 包含：拖拽上传、传输进度、批量传输、断点续传等功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import SparkMD5 from 'spark-md5'

export function useFileTransfer() {
  // ===== 拖拽上传相关 =====
  const isDragOver = ref(false)
  const dragCounter = ref(0) // 用于处理dragleave事件的计数器

  // 全局拖拽事件处理 - 阻止默认行为
  const handleGlobalDragOver = (event) => {
    event.preventDefault()
    event.stopPropagation()
  }

  const handleGlobalDrop = (event) => {
    event.preventDefault()
    event.stopPropagation()
  }

  const handleGlobalDragEnter = (event) => {
    event.preventDefault()
    event.stopPropagation()
  }

  const handleGlobalDragLeave = (event) => {
    event.preventDefault()
    event.stopPropagation()
  }

  // 在组件挂载时添加全局事件监听
  onMounted(() => {
    // 阻止整个页面的默认拖拽行为
    document.addEventListener('dragover', handleGlobalDragOver, false)
    document.addEventListener('drop', handleGlobalDrop, false)
    document.addEventListener('dragenter', handleGlobalDragEnter, false)
    document.addEventListener('dragleave', handleGlobalDragLeave, false)

    // 加载断点续传信息
    loadBreakpoints()
  })

  // 在组件卸载时移除全局事件监听
  onUnmounted(() => {
    document.removeEventListener('dragover', handleGlobalDragOver, false)
    document.removeEventListener('drop', handleGlobalDrop, false)
    document.removeEventListener('dragenter', handleGlobalDragEnter, false)
    document.removeEventListener('dragleave', handleGlobalDragLeave, false)
  })

  // 拖拽进入聊天区域
  const handleDragOver = (event) => {
    event.preventDefault()
    event.stopPropagation()
    event.dataTransfer.dropEffect = 'copy'
    
    if (!isDragOver.value) {
      isDragOver.value = true
    }
    dragCounter.value++
  }

  // 拖拽离开聊天区域
  const handleDragLeave = (event) => {
    event.preventDefault()
    event.stopPropagation()
    dragCounter.value--
    
    // 只有当所有拖拽事件都离开时才隐藏提示
    if (dragCounter.value <= 0) {
      isDragOver.value = false
      dragCounter.value = 0
    }
  }

  // 拖拽进入聊天区域
  const handleDragEnter = (event) => {
    event.preventDefault()
    event.stopPropagation()
    dragCounter.value++
    
    if (!isDragOver.value) {
      isDragOver.value = true
    }
  }

  // 处理文件拖拽放下
  const handleDrop = async (event, activeDevice, uploadFiles) => {
    event.preventDefault()
    event.stopPropagation()
    isDragOver.value = false
    dragCounter.value = 0

    if (!activeDevice) {
      console.error('拖拽上传失败：没有活动设备')
      ElMessage.error('请先选择设备')
      return
    }
    
    console.log('拖拽上传：活动设备', activeDevice.name, activeDevice.id)

    const files = Array.from(event.dataTransfer.files)
    if (files.length === 0) {
      return
    }

    // 检查文件数量
    if (files.length > 10) {
      ElMessage.warning('一次最多只能上传10个文件')
      return
    }

    // 批量上传文件
    await uploadFiles(files)
  }

  // ===== 传输进度相关 =====
  const transferProgress = ref(new Map()) // 存储每个文件的传输进度

  // 创建传输进度项
  const createProgressItem = (fileId, fileName, fileSize) => {
    const progressItem = {
      id: fileId,
      name: fileName,
      size: fileSize,
      transferred: 0,
      progress: 0,
      speed: 0,
      status: 'uploading', // uploading, completed, failed, paused
      startTime: Date.now(),
      lastTime: Date.now(),
      lastTransferred: 0
    }
    
    transferProgress.value.set(fileId, progressItem)
    return progressItem
  }

  // 更新传输进度
  const updateProgress = (fileId, transferred) => {
    const item = transferProgress.value.get(fileId)
    if (!item) return

    const now = Date.now()
    const timeDiff = now - item.lastTime
    const sizeDiff = transferred - item.lastTransferred

    // 计算传输速度 (bytes/second)
    if (timeDiff > 0) {
      item.speed = (sizeDiff / timeDiff) * 1000
    }

    item.transferred = transferred
    item.progress = Math.round((transferred / item.size) * 100)
    item.lastTime = now
    item.lastTransferred = transferred

    // 触发响应式更新
    transferProgress.value.set(fileId, { ...item })
  }

  // 完成传输
  const completeTransfer = (fileId) => {
    const item = transferProgress.value.get(fileId)
    if (item) {
      item.status = 'completed'
      item.progress = 100
      transferProgress.value.set(fileId, { ...item })
      
      // 3秒后移除进度项
      setTimeout(() => {
        transferProgress.value.delete(fileId)
      }, 3000)
    }
  }

  // 传输失败
  const failTransfer = (fileId, error) => {
    const item = transferProgress.value.get(fileId)
    if (item) {
      item.status = 'failed'
      item.error = error
      transferProgress.value.set(fileId, { ...item })
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化传输速度
  const formatSpeed = (bytesPerSecond) => {
    return formatFileSize(bytesPerSecond) + '/s'
  }

  // ===== 批量文件传输 =====
  const uploadQueue = ref([]) // 上传队列
  const maxConcurrent = ref(3) // 最大并发上传数
  const currentUploading = ref(0) // 当前上传中的文件数

  // 批量上传文件
  const uploadFiles = async (files, options = {}) => {
    const {
      maxFileSize = 20 * 1024 * 1024 * 1024, // 默认20GB
      uploadUrl,
      onSuccess,
      onError,
      messageStore,
      activeDevice,
      skipMessageAdd = false // 👈 新增参数，控制是否跳过消息添加
    } = options

    // 验证文件
    const validFiles = []
    for (const file of files) {
      if (file.size > maxFileSize) {
        ElMessage.error(`文件 "${file.name}" 超过大小限制`)
        continue
      }
      validFiles.push(file)
    }

    if (validFiles.length === 0) {
      return
    }

    // 添加到上传队列
    const uploadTasks = validFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      status: 'pending'
    }))

    uploadQueue.value.push(...uploadTasks)

    // 开始处理队列
    processUploadQueue(uploadUrl, onSuccess, onError, messageStore, activeDevice, skipMessageAdd)
  }

  // 处理上传队列
  const processUploadQueue = async (uploadUrl, onSuccess, onError, messageStore, activeDevice, skipMessageAdd) => {
    while (uploadQueue.value.length > 0 && currentUploading.value < maxConcurrent.value) {
      const task = uploadQueue.value.shift()
      if (!task) break

      currentUploading.value++
      uploadSingleFile(task, uploadUrl, onSuccess, onError, messageStore, activeDevice, skipMessageAdd)
        .finally(() => {
          currentUploading.value--
          // 继续处理队列
          if (uploadQueue.value.length > 0) {
            processUploadQueue(uploadUrl, onSuccess, onError, messageStore, activeDevice, skipMessageAdd)
          }
        })
    }
  }

  // 上传单个文件
  const uploadSingleFile = async (task, uploadUrl, onSuccess, onError, messageStore, activeDevice, skipMessageAdd) => {
    const { file } = task
    const fileId = task.id

    try {
      // 创建进度项
      createProgressItem(fileId, file.name, file.size)

      // 检查是否需要断点续传
      if (file.size >= RESUMABLE_THRESHOLD) {
        console.log(`大文件检测: ${file.name} (${formatFileSize(file.size)}), 启用断点续传`)

        const resumableInfo = await checkResumable(file)
        if (resumableInfo) {
          const { fileId: resumableFileId, fileMD5, breakpointInfo } = resumableInfo

          if (breakpointInfo) {
            console.log(`发现断点信息，继续上传: ${breakpointInfo.uploadedChunks.length}/${breakpointInfo.totalChunks} 分片已完成`)

            // 询问用户是否继续上传
            const shouldResume = await ElMessageBox.confirm(
              `检测到文件 "${file.name}" 的未完成传输，是否继续上传？`,
              '断点续传',
              {
                confirmButtonText: '继续上传',
                cancelButtonText: '重新上传',
                type: 'info'
              }
            ).catch(() => false)

            if (shouldResume) {
              await uploadFileWithChunks(
                file,
                resumableFileId,
                fileMD5,
                uploadUrl,
                (current, total) => {
                  const progress = Math.round((current / total) * 100)
                  updateProgress(fileId, (file.size * progress) / 100)
                },
                () => {
                  completeTransfer(fileId)
                  if (!skipMessageAdd && messageStore && activeDevice?.value) {
                    const message = {
                      type: 'file',
                      file: { name: file.name, size: file.size },
                      isRemote: false,
                      deviceId: activeDevice.value.id
                    }
                    messageStore.addMessage(message)
                  }
                  onSuccess?.({ success: true, data: { name: file.name, size: file.size } }, file)
                },
                (error) => {
                  failTransfer(fileId, error.message)
                  onError?.(error, file)
                },
                breakpointInfo
              )
              return
            } else {
              // 用户选择重新上传，清除断点信息
              clearBreakpoint(resumableFileId)
            }
          }

          // 开始新的分片上传
          await uploadFileWithChunks(
            file,
            resumableFileId,
            fileMD5,
            uploadUrl,
            (current, total) => {
              const progress = Math.round((current / total) * 100)
              updateProgress(fileId, (file.size * progress) / 100)
            },
            () => {
              completeTransfer(fileId)
              if (!skipMessageAdd && messageStore && activeDevice?.value) {
                const message = {
                  type: 'file',
                  file: { name: file.name, size: file.size },
                  isRemote: false,
                  deviceId: activeDevice.value.id
                }
                messageStore.addMessage(message)
              }
              onSuccess?.({ success: true, data: { name: file.name, size: file.size } }, file)
            },
            (error) => {
              failTransfer(fileId, error.message)
              onError?.(error, file)
            }
          )
          return
        }
      }

      // 小文件或不支持断点续传，使用传统上传方式
      const formData = new FormData()
      formData.append('file', file)

      const xhr = new XMLHttpRequest()

      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          updateProgress(fileId, event.loaded)
        }
      })

      // 上传完成
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const result = JSON.parse(xhr.responseText)
            if (result.success) {
              completeTransfer(fileId)

              // 添加到消息列表
              if (!skipMessageAdd && messageStore && activeDevice?.value) {
                const message = {
                  type: 'file',
                  file: result.data,
                  isRemote: false,
                  deviceId: activeDevice.value.id
                }
                messageStore.addMessage(message)
              }
              
              onSuccess?.(result, file)
            } else {
              failTransfer(fileId, result.message || '上传失败')
              onError?.(new Error(result.message || '上传失败'), file)
            }
          } catch (error) {
            failTransfer(fileId, '响应解析失败')
            onError?.(error, file)
          }
        } else {
          failTransfer(fileId, `HTTP ${xhr.status}`)
          onError?.(new Error(`HTTP ${xhr.status}`), file)
        }
      })

      // 上传失败
      xhr.addEventListener('error', () => {
        failTransfer(fileId, '网络错误')
        onError?.(new Error('网络错误'), file)
      })

      // 开始上传
      xhr.open('POST', uploadUrl)
      xhr.send(formData)

    } catch (error) {
      failTransfer(fileId, error.message)
      onError?.(error, file)
    }
  }

  // ===== 断点续传相关 =====
  const resumableTransfers = ref(new Map()) // 存储可续传的文件信息
  const CHUNK_SIZE = 2 * 1024 * 1024 // 2MB 分片大小
  const RESUMABLE_THRESHOLD = 10 * 1024 * 1024 // 10MB 以上的文件启用断点续传

  // 计算文件MD5（用于文件唯一标识）
  const calculateFileMD5 = async (file) => {
    return new Promise((resolve, reject) => {
      const spark = new SparkMD5.ArrayBuffer()
      const fileReader = new FileReader()
      const chunks = Math.ceil(file.size / CHUNK_SIZE)
      let currentChunk = 0

      fileReader.onload = (e) => {
        spark.append(e.target.result)
        currentChunk++

        if (currentChunk < chunks) {
          loadNext()
        } else {
          resolve(spark.end())
        }
      }

      fileReader.onerror = reject

      const loadNext = () => {
        const start = currentChunk * CHUNK_SIZE
        const end = Math.min(start + CHUNK_SIZE, file.size)
        fileReader.readAsArrayBuffer(file.slice(start, end))
      }

      loadNext()
    })
  }

  // 保存断点信息到本地存储
  const saveBreakpoint = (fileId, fileInfo) => {
    const breakpointInfo = {
      ...fileInfo,
      timestamp: Date.now()
    }

    resumableTransfers.value.set(fileId, breakpointInfo)

    // 保存到localStorage
    try {
      const stored = JSON.parse(localStorage.getItem('resumableTransfers') || '{}')
      stored[fileId] = breakpointInfo
      localStorage.setItem('resumableTransfers', JSON.stringify(stored))
    } catch (error) {
      console.error('保存断点信息失败:', error)
    }
  }

  // 从本地存储加载断点信息
  const loadBreakpoints = () => {
    try {
      const stored = JSON.parse(localStorage.getItem('resumableTransfers') || '{}')
      for (const [fileId, info] of Object.entries(stored)) {
        // 只加载24小时内的断点信息
        if (Date.now() - info.timestamp < 24 * 60 * 60 * 1000) {
          resumableTransfers.value.set(fileId, info)
        }
      }
    } catch (error) {
      console.error('加载断点信息失败:', error)
    }
  }

  // 清除断点信息
  const clearBreakpoint = (fileId) => {
    resumableTransfers.value.delete(fileId)

    try {
      const stored = JSON.parse(localStorage.getItem('resumableTransfers') || '{}')
      delete stored[fileId]
      localStorage.setItem('resumableTransfers', JSON.stringify(stored))
    } catch (error) {
      console.error('清除断点信息失败:', error)
    }
  }

  // 检查文件是否可以续传
  const checkResumable = async (file) => {
    if (file.size < RESUMABLE_THRESHOLD) {
      return null // 小文件不需要断点续传
    }

    const fileMD5 = await calculateFileMD5(file)
    const fileId = `${fileMD5}_${file.size}_${file.name}`
    const breakpointInfo = resumableTransfers.value.get(fileId)

    if (breakpointInfo && breakpointInfo.uploadedChunks) {
      return {
        fileId,
        fileMD5,
        breakpointInfo
      }
    }

    return {
      fileId,
      fileMD5,
      breakpointInfo: null
    }
  }

  // 分片上传文件
  const uploadFileWithChunks = async (file, fileId, fileMD5, uploadUrl, onProgress, onSuccess, onError, resumeInfo = null) => {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    const uploadedChunks = resumeInfo ? new Set(resumeInfo.uploadedChunks) : new Set()

    console.log(`开始分片上传: ${file.name}, 总分片数: ${totalChunks}, 已上传: ${uploadedChunks.size}`)

    // 保存断点信息
    const breakpointInfo = {
      fileName: file.name,
      fileSize: file.size,
      fileMD5,
      totalChunks,
      uploadedChunks: Array.from(uploadedChunks),
      uploadUrl
    }
    saveBreakpoint(fileId, breakpointInfo)

    // 上传分片
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      if (uploadedChunks.has(chunkIndex)) {
        // 跳过已上传的分片
        onProgress?.(chunkIndex + 1, totalChunks)
        continue
      }

      const start = chunkIndex * CHUNK_SIZE
      const end = Math.min(start + CHUNK_SIZE, file.size)
      const chunk = file.slice(start, end)

      try {
        await uploadChunk(chunk, chunkIndex, totalChunks, fileMD5, uploadUrl)
        uploadedChunks.add(chunkIndex)

        // 更新断点信息
        breakpointInfo.uploadedChunks = Array.from(uploadedChunks)
        saveBreakpoint(fileId, breakpointInfo)

        onProgress?.(chunkIndex + 1, totalChunks)
      } catch (error) {
        console.error(`分片 ${chunkIndex} 上传失败:`, error)
        onError?.(error)
        return
      }
    }

    // 所有分片上传完成，通知后端合并
    try {
      await mergeChunks(fileMD5, file.name, totalChunks, uploadUrl)
      clearBreakpoint(fileId)
      onSuccess?.()
    } catch (error) {
      console.error('文件合并失败:', error)
      onError?.(error)
    }
  }

  // 上传单个分片
  const uploadChunk = async (chunk, chunkIndex, totalChunks, fileMD5, uploadUrl) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('chunk', chunk)
      formData.append('chunkIndex', chunkIndex)
      formData.append('totalChunks', totalChunks)
      formData.append('fileMD5', fileMD5)

      const xhr = new XMLHttpRequest()

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const result = JSON.parse(xhr.responseText)
            if (result.success) {
              resolve(result)
            } else {
              reject(new Error(result.message || '分片上传失败'))
            }
          } catch (error) {
            reject(new Error('响应解析失败'))
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}`))
        }
      }

      xhr.onerror = () => reject(new Error('网络错误'))

      xhr.open('POST', `${uploadUrl}/chunk`)
      xhr.send(formData)
    })
  }

  // 合并分片
  const mergeChunks = async (fileMD5, fileName, totalChunks, uploadUrl) => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const result = JSON.parse(xhr.responseText)
            if (result.success) {
              resolve(result)
            } else {
              reject(new Error(result.message || '文件合并失败'))
            }
          } catch (error) {
            reject(new Error('响应解析失败'))
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}`))
        }
      }

      xhr.onerror = () => reject(new Error('网络错误'))

      xhr.open('POST', `${uploadUrl}/merge`)
      xhr.setRequestHeader('Content-Type', 'application/json')
      xhr.send(JSON.stringify({
        fileMD5,
        fileName,
        totalChunks
      }))
    })
  }

  // 恢复传输
  const resumeTransfer = async (fileId) => {
    const transferInfo = resumableTransfers.value.get(fileId)
    if (!transferInfo) {
      throw new Error('未找到断点信息')
    }

    console.log('恢复传输:', transferInfo)
    return transferInfo
  }

  // ===== 工具函数 =====
  
  // 获取活动的传输项
  const activeTransfers = computed(() => {
    return Array.from(transferProgress.value.values())
      .filter(item => item.status === 'uploading')
  })

  // 获取所有传输项
  const allTransfers = computed(() => {
    return Array.from(transferProgress.value.values())
  })

  // 清理完成的传输
  const clearCompletedTransfers = () => {
    for (const [fileId, item] of transferProgress.value.entries()) {
      if (item.status === 'completed') {
        transferProgress.value.delete(fileId)
      }
    }
  }

  return {
    // 拖拽上传
    isDragOver,
    handleDragOver,
    handleDragLeave,
    handleDragEnter,
    handleDrop,
    
    // 传输进度
    transferProgress,
    createProgressItem,
    updateProgress,
    completeTransfer,
    failTransfer,
    formatFileSize,
    formatSpeed,
    
    // 批量传输
    uploadFiles,
    uploadQueue,
    currentUploading,
    maxConcurrent,
    
    // 断点续传
    resumableTransfers,
    saveBreakpoint,
    resumeTransfer,
    loadBreakpoints,
    clearBreakpoint,
    checkResumable,
    calculateFileMD5,
    uploadFileWithChunks,
    
    // 计算属性
    activeTransfers,
    allTransfers,
    
    // 工具函数
    clearCompletedTransfers
  }
}